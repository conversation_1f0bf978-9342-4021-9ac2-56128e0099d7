use tauri::{
    tray::{<PERSON><PERSON><PERSON><PERSON>, MouseButtonState, TrayIconBuilder, TrayIconEvent},
    menu::{Menu, MenuItem},
    Manager, Runtime, AppHandle, Emitter
};
use std::sync::Mutex;

// 全局状态管理
static TRAY_INITIALIZED: Mutex<bool> = Mutex::new(false);

/// 初始化系统托盘
#[tauri::command]
pub async fn init_system_tray<R: Runtime>(app: AppHandle<R>) -> Result<(), String> {
    let mut initialized = TRAY_INITIALIZED.lock().map_err(|e| e.to_string())?;
    
    if *initialized {
        return Ok(());
    }

    // 创建托盘菜单
    let show_item = MenuItem::with_id(&app, "show", "显示窗口", true, None::<&str>)
        .map_err(|e| format!("创建显示菜单项失败: {}", e))?;
    
    let hide_item = MenuItem::with_id(&app, "hide", "隐藏窗口", true, None::<&str>)
        .map_err(|e| format!("创建隐藏菜单项失败: {}", e))?;
    
    let separator = MenuItem::separator(&app)
        .map_err(|e| format!("创建分隔符失败: {}", e))?;
    
    let quit_item = MenuItem::with_id(&app, "quit", "退出", true, None::<&str>)
        .map_err(|e| format!("创建退出菜单项失败: {}", e))?;

    let menu = Menu::with_items(&app, &[&show_item, &hide_item, &separator, &quit_item])
        .map_err(|e| format!("创建托盘菜单失败: {}", e))?;

    // 创建托盘图标
    let _tray = TrayIconBuilder::with_id("main-tray")
        .menu(&menu)
        .icon(app.default_window_icon().unwrap().clone())
        .tooltip("EmbedTalk")
        .on_menu_event(move |app, event| {
            match event.id.as_ref() {
                "show" => {
                    if let Some(window) = app.get_webview_window("main") {
                        let _ = window.show();
                        let _ = window.set_focus();
                    }
                }
                "hide" => {
                    if let Some(window) = app.get_webview_window("main") {
                        let _ = window.hide();
                    }
                }
                "quit" => {
                    app.exit(0);
                }
                _ => {}
            }
        })
        .on_tray_icon_event(|tray, event| {
            match event {
                TrayIconEvent::Click {
                    button: MouseButton::Left,
                    button_state: MouseButtonState::Up,
                    ..
                } => {
                    let app = tray.app_handle();
                    if let Some(window) = app.get_webview_window("main") {
                        if window.is_visible().unwrap_or(false) {
                            let _ = window.hide();
                        } else {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                }
                TrayIconEvent::DoubleClick {
                    button: MouseButton::Left,
                    ..
                } => {
                    let app = tray.app_handle();
                    if let Some(window) = app.get_webview_window("main") {
                        let _ = window.show();
                        let _ = window.set_focus();
                    }
                }
                _ => {}
            }
        })
        .build(&app)
        .map_err(|e| format!("构建托盘图标失败: {}", e))?;

    *initialized = true;
    Ok(())
}

/// 显示托盘通知
#[tauri::command]
pub async fn show_tray_notification(title: String, message: String) -> Result<(), String> {
    // 在Tauri v2中，通知功能可能需要额外的插件
    // 这里先用简单的日志记录
    println!("托盘通知: {} - {}", title, message);
    Ok(())
}

/// 退出应用程序
#[tauri::command]
pub async fn exit_app<R: Runtime>(app: AppHandle<R>) -> Result<(), String> {
    app.exit(0);
    Ok(())
}

/// 显示主窗口
#[tauri::command]
pub async fn show_main_window<R: Runtime>(app: AppHandle<R>) -> Result<(), String> {
    if let Some(window) = app.get_webview_window("main") {
        window.show().map_err(|e| format!("显示窗口失败: {}", e))?;
        window.set_focus().map_err(|e| format!("设置窗口焦点失败: {}", e))?;
    }
    Ok(())
}

/// 隐藏主窗口
#[tauri::command]
pub async fn hide_main_window<R: Runtime>(app: AppHandle<R>) -> Result<(), String> {
    if let Some(window) = app.get_webview_window("main") {
        window.hide().map_err(|e| format!("隐藏窗口失败: {}", e))?;
    }
    Ok(())
}

/// 检查窗口是否可见
#[tauri::command]
pub async fn is_window_visible<R: Runtime>(app: AppHandle<R>) -> Result<bool, String> {
    if let Some(window) = app.get_webview_window("main") {
        window.is_visible().map_err(|e| format!("检查窗口可见性失败: {}", e))
    } else {
        Ok(false)
    }
}
