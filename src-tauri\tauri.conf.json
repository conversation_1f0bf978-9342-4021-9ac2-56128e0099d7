{"$schema": "https://schema.tauri.app/config/2", "productName": "EmbedTalk", "version": "0.0.11", "identifier": "emabletalk", "build": {"beforeDevCommand": "pnpm run dev", "beforeBuildCommand": "pnpm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["./resources/"], "createUpdaterArtifacts": true}, "plugins": {"updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDZDNUI0NTYxQzkxRDc3QzQKUldURWR4M0pZVVZiYkx1bkd2WG93MFBsY3pCUkZudCtRbzhsZysvWEh2S1EzV2pCK1ZWOVErN2IK", "endpoints": ["https://github.com/zerojacks/EmbedTalk/releases/latest/download/latest.json"], "windows": {"installMode": "passive"}}}, "app": {"security": {"csp": null}, "trayIcon": {"iconPath": "icons/icon.ico", "iconAsTemplate": false, "menuOnLeftClick": false, "tooltip": "EmbedTalk"}, "windows": [{"label": "main", "decorations": true, "fullscreen": false, "resizable": true, "title": "EmbedTalk", "width": 800, "height": 600, "minWidth": 800, "minHeight": 600, "transparent": true, "center": true}], "macOSPrivateApi": true}}