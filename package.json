{"name": "embedtalk", "private": true, "version": "0.0.11", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@heroicons/react": "^1.0.6", "@minoru/react-dnd-treeview": "^3.5.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^6.1.5", "@mui/material": "^6.1.5", "@mui/x-date-pickers": "^7.28.3", "@radix-ui/react-dropdown-menu": "^2.1.7", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.13.6", "@tauri-apps/plugin-clipboard-manager": "^2.0.0", "@tauri-apps/plugin-dialog": "^2.0.1", "@tauri-apps/plugin-fs": "^2.0.1", "@tauri-apps/plugin-global-shortcut": "^2.2.0", "@tauri-apps/plugin-log": "~2", "@tauri-apps/plugin-os": "^2.0.0", "@tauri-apps/plugin-process": "^2.0.0", "@tauri-apps/plugin-shell": "^2.0.1", "@tauri-apps/plugin-sql": "^2.2.0", "@tauri-apps/plugin-updater": "~2.7.1", "@tauri-apps/plugin-window-state": "~2.2.2", "@types/file-saver": "^2.0.7", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-table": "^7.7.20", "@types/xlsx": "^0.0.36", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^1.2.1", "dom-to-image": "^2.6.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.452.0", "match-sorter": "^6.3.4", "material-react-table": "^3.2.1", "monaco-editor": "^0.52.0", "postcss": "^8.4.47", "primereact": "^10.8.4", "react": "^19.1.0", "react-data-grid": "7.0.0-beta.51", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.27.0", "react-split": "^2.0.14", "react-syntax-highlighter": "^15.6.1", "react-table": "^7.8.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "remark-gfm": "^4.0.1", "sort-by": "^1.2.0", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "theme-change": "^2.5.0", "uuid": "^10.0.0", "xlsx": "^0.18.5", "zustand": "^5.0.0"}, "devDependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/typography": "^0.5.15", "@tauri-apps/api": "^2.5.0", "@tauri-apps/cli": "2.5.0", "@types/dom-to-image": "^2.6.7", "@types/lodash": "^4.17.14", "@types/node": "^18.19.59", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-resizable": "^3.0.8", "@types/react-virtualized-auto-sizer": "^1.0.8", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.5.0", "daisyui": "^4.12.13", "typescript": "^4.9.5", "vite": "^6.3.5", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-static-copy": "^2.3.1"}}