D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\target\debug\deps\libEmbedTalk-4d7214a1d67587b0.rmeta: src\main.rs src\basefunc\mod.rs src\basefunc\frame_645.rs src\basefunc\frame_cco.rs src\basefunc\frame_csg.rs src\basefunc\frame_err.rs src\basefunc\frame_fun.rs src\basefunc\frame_moudle.rs src\basefunc\frame_speecial.rs src\basefunc\frame_tctask.rs src\basefunc\protocol.rs src\config\mod.rs src\config\appconfig.rs src\config\constants.rs src\config\oadmapconfig.rs src\config\xmlconfig.rs src\combridage\mod.rs src\combridage\bluetooth.rs src\combridage\commanger.rs src\combridage\messagemanager.rs src\combridage\mqtt.rs src\combridage\serial_port.rs src\combridage\tcp_client.rs src\combridage\tcp_server.rs src\taurihandler\mod.rs src\taurihandler\channel_handler.rs src\taurihandler\dlt645_handler.rs src\taurihandler\handler.rs src\taurihandler\protocol_handler.rs src\protocol\mod.rs src\protocol\channel_adapter.rs src\protocol\channel_handler.rs src\protocol\dlt645\mod.rs src\protocol\dlt645\builder.rs src\protocol\dlt645\parser.rs src\protocol\manager.rs src\protocol\modbus\mod.rs src\protocol\modbus\builder.rs src\protocol\modbus\parser.rs src\protocol\traits.rs src\global\mod.rs D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\target\debug\build\EmbedTalk-67a5e2fea740cf5e\out/54122cc9100e07ff11c50b040157f124e55cbc6633d227077027cb02d0fbbc63

D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\target\debug\deps\EmbedTalk-4d7214a1d67587b0.d: src\main.rs src\basefunc\mod.rs src\basefunc\frame_645.rs src\basefunc\frame_cco.rs src\basefunc\frame_csg.rs src\basefunc\frame_err.rs src\basefunc\frame_fun.rs src\basefunc\frame_moudle.rs src\basefunc\frame_speecial.rs src\basefunc\frame_tctask.rs src\basefunc\protocol.rs src\config\mod.rs src\config\appconfig.rs src\config\constants.rs src\config\oadmapconfig.rs src\config\xmlconfig.rs src\combridage\mod.rs src\combridage\bluetooth.rs src\combridage\commanger.rs src\combridage\messagemanager.rs src\combridage\mqtt.rs src\combridage\serial_port.rs src\combridage\tcp_client.rs src\combridage\tcp_server.rs src\taurihandler\mod.rs src\taurihandler\channel_handler.rs src\taurihandler\dlt645_handler.rs src\taurihandler\handler.rs src\taurihandler\protocol_handler.rs src\protocol\mod.rs src\protocol\channel_adapter.rs src\protocol\channel_handler.rs src\protocol\dlt645\mod.rs src\protocol\dlt645\builder.rs src\protocol\dlt645\parser.rs src\protocol\manager.rs src\protocol\modbus\mod.rs src\protocol\modbus\builder.rs src\protocol\modbus\parser.rs src\protocol\traits.rs src\global\mod.rs D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\target\debug\build\EmbedTalk-67a5e2fea740cf5e\out/54122cc9100e07ff11c50b040157f124e55cbc6633d227077027cb02d0fbbc63

src\main.rs:
src\basefunc\mod.rs:
src\basefunc\frame_645.rs:
src\basefunc\frame_cco.rs:
src\basefunc\frame_csg.rs:
src\basefunc\frame_err.rs:
src\basefunc\frame_fun.rs:
src\basefunc\frame_moudle.rs:
src\basefunc\frame_speecial.rs:
src\basefunc\frame_tctask.rs:
src\basefunc\protocol.rs:
src\config\mod.rs:
src\config\appconfig.rs:
src\config\constants.rs:
src\config\oadmapconfig.rs:
src\config\xmlconfig.rs:
src\combridage\mod.rs:
src\combridage\bluetooth.rs:
src\combridage\commanger.rs:
src\combridage\messagemanager.rs:
src\combridage\mqtt.rs:
src\combridage\serial_port.rs:
src\combridage\tcp_client.rs:
src\combridage\tcp_server.rs:
src\taurihandler\mod.rs:
src\taurihandler\channel_handler.rs:
src\taurihandler\dlt645_handler.rs:
src\taurihandler\handler.rs:
src\taurihandler\protocol_handler.rs:
src\protocol\mod.rs:
src\protocol\channel_adapter.rs:
src\protocol\channel_handler.rs:
src\protocol\dlt645\mod.rs:
src\protocol\dlt645\builder.rs:
src\protocol\dlt645\parser.rs:
src\protocol\manager.rs:
src\protocol\modbus\mod.rs:
src\protocol\modbus\builder.rs:
src\protocol\modbus\parser.rs:
src\protocol\traits.rs:
src\global\mod.rs:
D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\target\debug\build\EmbedTalk-67a5e2fea740cf5e\out/54122cc9100e07ff11c50b040157f124e55cbc6633d227077027cb02d0fbbc63:

# env-dep:CARGO_PKG_AUTHORS=zerojack
# env-dep:CARGO_PKG_DESCRIPTION=EmbedTalk
# env-dep:CARGO_PKG_NAME=EmbedTalk
# env-dep:CARGO_PKG_VERSION=0.0.11
# env-dep:OUT_DIR=D:\\ProjackSpace\\projectspace\\EmbedTalk\\src-tauri\\target\\debug\\build\\EmbedTalk-67a5e2fea740cf5e\\out
