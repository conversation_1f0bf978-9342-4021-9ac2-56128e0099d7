import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTrayManager } from '../hooks/useTrayManager';
import { 
    selectCloseToTray, 
    selectMinimizeToTray,
    setCloseToTray,
    setMinimizeToTray 
} from '../store/slices/settingsSlice';

/**
 * 托盘功能使用示例
 * 
 * 这个组件展示了如何在应用程序中集成和使用托盘功能
 */
export const TrayUsageExample: React.FC = () => {
    const dispatch = useDispatch();
    const { handleMinimizeToTray, handleExitApp } = useTrayManager();
    
    // 获取当前托盘设置
    const closeToTray = useSelector(selectCloseToTray);
    const minimizeToTray = useSelector(selectMinimizeToTray);

    // 示例：自定义关闭处理
    const handleCustomClose = () => {
        if (closeToTray) {
            handleMinimizeToTray();
        } else {
            handleExitApp();
        }
    };

    // 示例：切换托盘设置
    const toggleCloseToTray = () => {
        dispatch(setCloseToTray(!closeToTray));
    };

    const toggleMinimizeToTray = () => {
        dispatch(setMinimizeToTray(!minimizeToTray));
    };

    return (
        <div className="p-4 space-y-4">
            <h2 className="text-xl font-bold">托盘功能使用示例</h2>
            
            {/* 当前设置显示 */}
            <div className="card bg-base-200">
                <div className="card-body">
                    <h3 className="card-title text-sm">当前设置</h3>
                    <div className="space-y-2 text-sm">
                        <div>关闭到托盘: {closeToTray ? '启用' : '禁用'}</div>
                        <div>最小化到托盘: {minimizeToTray ? '启用' : '禁用'}</div>
                    </div>
                </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-2">
                <button 
                    className="btn btn-primary btn-sm w-full"
                    onClick={handleMinimizeToTray}
                >
                    最小化到托盘
                </button>
                
                <button 
                    className="btn btn-secondary btn-sm w-full"
                    onClick={handleCustomClose}
                >
                    智能关闭 (根据设置)
                </button>
                
                <button 
                    className="btn btn-accent btn-sm w-full"
                    onClick={toggleCloseToTray}
                >
                    切换关闭到托盘设置
                </button>
                
                <button 
                    className="btn btn-info btn-sm w-full"
                    onClick={toggleMinimizeToTray}
                >
                    切换最小化到托盘设置
                </button>
            </div>

            {/* 代码示例 */}
            <div className="mockup-code text-xs">
                <pre data-prefix="1"><code>// 1. 导入必要的hooks和selectors</code></pre>
                <pre data-prefix="2"><code>import {`{`} useTrayManager {`}`} from '../hooks/useTrayManager';</code></pre>
                <pre data-prefix="3"><code>import {`{`} selectCloseToTray {`}`} from '../store/slices/settingsSlice';</code></pre>
                <pre data-prefix="4"><code></code></pre>
                <pre data-prefix="5"><code>// 2. 在组件中使用</code></pre>
                <pre data-prefix="6"><code>const {`{`} handleMinimizeToTray {`}`} = useTrayManager();</code></pre>
                <pre data-prefix="7"><code>const closeToTray = useSelector(selectCloseToTray);</code></pre>
                <pre data-prefix="8"><code></code></pre>
                <pre data-prefix="9"><code>// 3. 调用托盘功能</code></pre>
                <pre data-prefix="10"><code>handleMinimizeToTray(); // 最小化到托盘</code></pre>
            </div>
        </div>
    );
};

/**
 * 在主应用程序中集成托盘功能的步骤：
 * 
 * 1. 确保TrayProvider包装了整个应用程序 (已在main.tsx中完成)
 * 2. 在需要的组件中使用useTrayManager hook
 * 3. 使用Redux selectors获取托盘设置
 * 4. 调用相应的托盘操作函数
 * 
 * 示例集成代码：
 * 
 * ```tsx
 * import { useTrayManager } from './hooks/useTrayManager';
 * import { useSelector } from 'react-redux';
 * import { selectCloseToTray } from './store/slices/settingsSlice';
 * 
 * function MyComponent() {
 *   const { handleMinimizeToTray, handleExitApp } = useTrayManager();
 *   const closeToTray = useSelector(selectCloseToTray);
 * 
 *   const handleClose = () => {
 *     if (closeToTray) {
 *       handleMinimizeToTray();
 *     } else {
 *       handleExitApp();
 *     }
 *   };
 * 
 *   return (
 *     <button onClick={handleClose}>
 *       关闭应用程序
 *     </button>
 *   );
 * }
 * ```
 */
