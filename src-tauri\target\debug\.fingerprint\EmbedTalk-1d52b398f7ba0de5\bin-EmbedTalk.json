{"rustc": 16591470773350601817, "features": "[\"cocoa\", \"desktop\", \"tauri\", \"tauri-plugin-clipboard-manager\", \"tauri-plugin-dialog\", \"tauri-plugin-fs\", \"tauri-plugin-global-shortcut\", \"tauri-plugin-log\", \"tauri-plugin-os\", \"tauri-plugin-process\", \"tauri-plugin-shell\", \"tauri-plugin-sql\", \"tauri-plugin-updater\", \"tauri-plugin-window-state\"]", "declared_features": "[\"axum\", \"cocoa\", \"custom-protocol\", \"default\", \"desktop\", \"tauri\", \"tauri-plugin-clipboard-manager\", \"tauri-plugin-dialog\", \"tauri-plugin-fs\", \"tauri-plugin-global-shortcut\", \"tauri-plugin-log\", \"tauri-plugin-os\", \"tauri-plugin-process\", \"tauri-plugin-shell\", \"tauri-plugin-sql\", \"tauri-plugin-updater\", \"tauri-plugin-window-state\", \"tower\", \"tower-http\", \"web\"]", "target": 7626640634717798762, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[65234016722529558, "bincode", false, 6761056139692697933], [371826037427084582, "tauri_plugin_clipboard_manager", false, 1012493523491353977], [530211389790465181, "hex", false, 11684061116120551390], [1211321333142909612, "socket2", false, 6778455900809151169], [1462335029370885857, "quick_xml", false, 8707019719147780311], [1582828171158827377, "tauri_plugin_shell", false, 9753115486475137975], [3722963349756955755, "once_cell", false, 14316785967451697991], [4368795134222496747, "tauri_plugin_sql", false, 7269225300228167905], [4450062412064442726, "dirs_next", false, 10406015873111567205], [5157003953992891593, "tauri_plugin_dialog", false, 14738748863336042063], [5157631553186200874, "num_traits", false, 11375847848648935747], [5584066344745166980, "btleplug", false, 6068947778083142396], [5617510748442681000, "backtrace", false, 17838785304294287271], [5990956534088275425, "num_derive", false, 5488996274789918991], [6662727387107639740, "objc", false, 17806367857650140020], [7236291379133587555, "tauri_plugin_log", false, 10587942295702677842], [7303982924001358866, "tokio", false, 3023819840456453703], [7799778466509000297, "windows", false, 10474446752993660236], [8269115081296425610, "uuid", false, 17234719483207582000], [8443559281687440230, "erased_serde", false, 17603160634501779780], [8606274917505247608, "tracing", false, 12464077446396853760], [9280368297895604912, "toml", false, 8103426468312335916], [9451456094439810778, "regex", false, 13023028693139711423], [9578864029216354803, "build_script_build", false, 10467083007181762446], [9614479274285663593, "serde_yaml", false, 5645353479966371988], [9689903380558560274, "serde", false, 181836187879074760], [9897246384292347999, "chrono", false, 8027097534737490404], [10436516057248261217, "tauri_plugin_global_shortcut", false, 146057646787221187], [10697383615564341592, "rayon", false, 16048225160114826782], [10755362358622467486, "tauri", false, 12901007708143624461], [11594979262886006466, "tracing_appender", false, 2240716231853627967], [11721252211900136025, "tauri_plugin_updater", false, 2729761242087541920], [11946729385090170470, "async_trait", false, 345523450662325525], [12676100885892732016, "tauri_plugin_os", false, 489800440441814836], [13016498663645373591, "tokio_serial", false, 345042624162817643], [13592916204794590741, "tauri_plugin_fs", false, 10310360745341295217], [14950883590652370704, "time", false, 17055368905950648685], [15367738274754116744, "serde_json", false, 300616874852894591], [16230660778393187092, "tracing_subscriber", false, 1401864461199129458], [16445525635439251275, "tauri_plugin_window_state", false, 9080015091027704983], [17509843537913359226, "tauri_plugin_process", false, 12381410220093019755], [17917672826516349275, "lazy_static", false, 16387329674517999961], [18062227174639142625, "rumqttc", false, 3714102451968514713]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\EmbedTalk-1d52b398f7ba0de5\\dep-bin-EmbedTalk", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}