import React, { useEffect } from 'react';
import { listen } from '@tauri-apps/api/event';
import { useTrayManager } from '../../hooks/useTrayManager';
import { CloseConfirmDialog } from '../ui/CloseConfirmDialog';

interface TrayManagerProps {
    children: React.ReactNode;
}

export const TrayManager: React.FC<TrayManagerProps> = ({ children }) => {
    const {
        showCloseDialog,
        setShowCloseDialog,
        handleWindowClose,
        handleMinimizeToTray,
        handleExitApp
    } = useTrayManager();

    useEffect(() => {
        // 监听窗口关闭请求事件
        const unlisten = listen('window-close-requested', () => {
            console.log('收到窗口关闭请求');
            handleWindowClose();
        });

        return () => {
            unlisten.then(fn => fn());
        };
    }, [handleWindowClose]);

    return (
        <>
            {children}
            <CloseConfirmDialog
                isOpen={showCloseDialog}
                onClose={() => setShowCloseDialog(false)}
                onMinimizeToTray={handleMinimizeToTray}
                onExitApp={handleExitApp}
            />
        </>
    );
};
