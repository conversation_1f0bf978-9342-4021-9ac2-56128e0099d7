{"$message_type":"diagnostic","message":"unused import: `Emitter`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\taurihandler\\tray_handler.rs","byte_start":149,"byte_end":156,"line_start":4,"line_end":4,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"    Manager, Runtime, AppHandle, Emitter","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\taurihandler\\tray_handler.rs","byte_start":147,"byte_end":156,"line_start":4,"line_end":4,"column_start":32,"column_end":41,"is_primary":true,"text":[{"text":"    Manager, Runtime, AppHandle, Emitter","highlight_start":32,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Emitter`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\taurihandler\\tray_handler.rs:4:34\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Manager, Runtime, AppHandle, Emitter\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `separator` found for struct `tauri::menu::MenuItem` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\taurihandler\\tray_handler.rs","byte_start":911,"byte_end":920,"line_start":27,"line_end":27,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"    let separator = MenuItem::separator(&app)","highlight_start":31,"highlight_end":40}],"label":"function or associated item not found in `MenuItem<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `tauri::menu::MenuItem<_>` consider using one of the following associated functions:\nmenu::normal::<impl tauri::menu::MenuItem<R>>::new\nmenu::normal::<impl tauri::menu::MenuItem<R>>::with_id","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.5.1\\src\\menu\\normal.rs","byte_start":598,"byte_end":787,"line_start":19,"line_end":28,"column_start":3,"column_end":19,"is_primary":true,"text":[{"text":"  pub fn new<M, T, A>(","highlight_start":3,"highlight_end":1},{"text":"    manager: &M,","highlight_start":1,"highlight_end":1},{"text":"    text: T,","highlight_start":1,"highlight_end":1},{"text":"    enabled: bool,","highlight_start":1,"highlight_end":1},{"text":"    accelerator: Option<A>,","highlight_start":1,"highlight_end":1},{"text":"  ) -> crate::Result<Self>","highlight_start":1,"highlight_end":1},{"text":"  where","highlight_start":1,"highlight_end":1},{"text":"    M: Manager<R>,","highlight_start":1,"highlight_end":1},{"text":"    T: AsRef<str>,","highlight_start":1,"highlight_end":1},{"text":"    A: AsRef<str>,","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.5.1\\src\\menu\\normal.rs","byte_start":1501,"byte_end":1729,"line_start":52,"line_end":63,"column_start":3,"column_end":19,"is_primary":true,"text":[{"text":"  pub fn with_id<M, I, T, A>(","highlight_start":3,"highlight_end":30},{"text":"    manager: &M,","highlight_start":1,"highlight_end":17},{"text":"    id: I,","highlight_start":1,"highlight_end":11},{"text":"    text: T,","highlight_start":1,"highlight_end":13},{"text":"    enabled: bool,","highlight_start":1,"highlight_end":19},{"text":"    accelerator: Option<A>,","highlight_start":1,"highlight_end":28},{"text":"  ) -> crate::Result<Self>","highlight_start":1,"highlight_end":27},{"text":"  where","highlight_start":1,"highlight_end":8},{"text":"    M: Manager<R>,","highlight_start":1,"highlight_end":19},{"text":"    I: Into<MenuId>,","highlight_start":1,"highlight_end":21},{"text":"    T: AsRef<str>,","highlight_start":1,"highlight_end":19},{"text":"    A: AsRef<str>,","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `separator` found for struct `tauri::menu::MenuItem` in the current scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\taurihandler\\tray_handler.rs:27:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let separator = MenuItem::separator(&app)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `MenuItem<_>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `tauri::menu::MenuItem<_>` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      menu::normal::<impl tauri::menu::MenuItem<R>>::new\u001b[0m\n\u001b[0m      menu::normal::<impl tauri::menu::MenuItem<R>>::with_id\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.5.1\\src\\menu\\normal.rs:19:3\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m  pub fn new<M, T, A>(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    manager: &M,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    text: T,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    enabled: bool,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    T: AsRef<str>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    A: AsRef<str>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|__________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m  pub fn with_id<M, I, T, A>(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    manager: &M,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    id: I,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    text: T,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    T: AsRef<str>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    A: AsRef<str>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|__________________^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `emit` found for reference `&tauri::Window` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":4255,"byte_end":4259,"line_start":104,"line_end":104,"column_start":36,"column_end":40,"is_primary":true,"text":[{"text":"                    let _ = window.emit(\"window-close-requested\", ());","highlight_start":36,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is a method `emit_to` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.5.1\\src\\lib.rs","byte_start":34583,"byte_end":34716,"line_start":988,"line_end":991,"column_start":3,"column_end":26,"is_primary":true,"text":[{"text":"  fn emit_to<I, S>(&self, target: I, event: &str, payload: S) -> Result<()>","highlight_start":3,"highlight_end":1},{"text":"  where","highlight_start":1,"highlight_end":1},{"text":"    I: Into<EventTarget>,","highlight_start":1,"highlight_end":1},{"text":"    S: Serialize + Clone,","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"trait `Emitter` which provides `emit` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":240,"byte_end":240,"line_start":5,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::panic;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use tauri::Emitter;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `emit` found for reference `&tauri::Window` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:104:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let _ = window.emit(\"window-close-requested\", ());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `emit_to` with a similar name, but with different arguments\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.5.1\\src\\lib.rs:988:3\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m988\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m  fn emit_to<I, S>(&self, target: I, event: &str, payload: S) -> Result<()>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m989\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m  where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m990\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    I: Into<EventTarget>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m991\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    S: Serialize + Clone,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `Emitter` which provides `emit` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use tauri::Emitter;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0599`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0599`.\u001b[0m\n"}
