import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectCloseToTray, selectMinimizeToTray, selectShowTrayNotifications } from '../store/slices/settingsSlice';
import { invoke } from '@tauri-apps/api/core';
import { getCurrentWindow } from '@tauri-apps/api/window';

export interface TrayManager {
    showCloseDialog: boolean;
    setShowCloseDialog: (show: boolean) => void;
    handleWindowClose: () => void;
    handleMinimizeToTray: () => void;
    handleExitApp: () => void;
    showWindow: () => void;
    hideWindow: () => void;
}

export const useTrayManager = (): TrayManager => {
    const [showCloseDialog, setShowCloseDialog] = useState(false);
    const closeToTray = useSelector(selectCloseToTray);
    const minimizeToTray = useSelector(selectMinimizeToTray);
    const showTrayNotifications = useSelector(selectShowTrayNotifications);

    // 初始化托盘
    useEffect(() => {
        const initTray = async () => {
            try {
                await invoke('init_system_tray');
                console.log('系统托盘初始化成功');
            } catch (error) {
                console.error('系统托盘初始化失败:', error);
            }
        };

        // 延迟初始化，确保应用完全加载
        const timer = setTimeout(initTray, 1000);
        return () => clearTimeout(timer);
    }, []);

    // 处理窗口关闭事件
    const handleWindowClose = () => {
        if (closeToTray === null || closeToTray === undefined) {
            // 如果用户还没有设置偏好，显示对话框
            setShowCloseDialog(true);
        } else if (closeToTray) {
            // 用户选择了最小化到托盘
            handleMinimizeToTray();
        } else {
            // 用户选择了完全退出
            handleExitApp();
        }
    };

    // 最小化到托盘
    const handleMinimizeToTray = async () => {
        try {
            const window = getCurrentWindow();
            await window.hide();
            
            if (showTrayNotifications) {
                await invoke('show_tray_notification', {
                    title: 'EmbedTalk',
                    message: '应用程序已最小化到系统托盘'
                });
            }
            
            console.log('窗口已隐藏到托盘');
        } catch (error) {
            console.error('隐藏窗口失败:', error);
        }
    };

    // 完全退出应用
    const handleExitApp = async () => {
        try {
            await invoke('exit_app');
        } catch (error) {
            console.error('退出应用失败:', error);
            // 如果Tauri命令失败，使用备用方法
            const window = getCurrentWindow();
            await window.close();
        }
    };

    // 显示窗口
    const showWindow = async () => {
        try {
            const window = getCurrentWindow();
            await window.show();
            await window.setFocus();
            console.log('窗口已显示');
        } catch (error) {
            console.error('显示窗口失败:', error);
        }
    };

    // 隐藏窗口
    const hideWindow = async () => {
        try {
            const window = getCurrentWindow();
            await window.hide();
            console.log('窗口已隐藏');
        } catch (error) {
            console.error('隐藏窗口失败:', error);
        }
    };

    return {
        showCloseDialog,
        setShowCloseDialog,
        handleWindowClose,
        handleMinimizeToTray,
        handleExitApp,
        showWindow,
        hideWindow
    };
};
