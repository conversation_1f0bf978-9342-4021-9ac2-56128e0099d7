import { useState } from 'react';
import { useSelector } from 'react-redux';
import { selectCloseToTray, selectMinimizeToTray, selectShowTrayNotifications } from '../store/slices/settingsSlice';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { exit } from '@tauri-apps/plugin-process';

export interface TrayManager {
    showCloseDialog: boolean;
    setShowCloseDialog: (show: boolean) => void;
    handleWindowClose: () => void;
    handleMinimizeToTray: () => void;
    handleExitApp: () => void;
    showWindow: () => void;
    hideWindow: () => void;
}

export const useTrayManager = (): TrayManager => {
    const [showCloseDialog, setShowCloseDialog] = useState(false);
    const closeToTray = useSelector(selectCloseToTray);
    const minimizeToTray = useSelector(selectMinimizeToTray);
    const showTrayNotifications = useSelector(selectShowTrayNotifications);

    // 托盘初始化由TrayProvider处理，这里不需要重复初始化

    // 处理窗口关闭事件
    const handleWindowClose = () => {
        if (closeToTray === null || closeToTray === undefined) {
            // 如果用户还没有设置偏好，显示对话框
            setShowCloseDialog(true);
        } else if (closeToTray) {
            // 用户选择了最小化到托盘
            handleMinimizeToTray();
        } else {
            // 用户选择了完全退出
            handleExitApp();
        }
    };

    // 最小化到托盘
    const handleMinimizeToTray = async () => {
        try {
            const window = getCurrentWindow();
            await window.hide();

            if (showTrayNotifications) {
                // 简单的控制台通知，可以后续扩展为系统通知
                console.log('EmbedTalk - 应用程序已最小化到系统托盘');
            }

            console.log('窗口已隐藏到托盘');
        } catch (error) {
            console.error('隐藏窗口失败:', error);
        }
    };

    // 完全退出应用
    const handleExitApp = async () => {
        try {
            await exit(0);
        } catch (error) {
            console.error('退出应用失败:', error);
            // 如果exit失败，使用备用方法
            const window = getCurrentWindow();
            await window.close();
        }
    };

    // 显示窗口
    const showWindow = async () => {
        try {
            const window = getCurrentWindow();
            await window.show();
            await window.setFocus();
            console.log('窗口已显示');
        } catch (error) {
            console.error('显示窗口失败:', error);
        }
    };

    // 隐藏窗口
    const hideWindow = async () => {
        try {
            const window = getCurrentWindow();
            await window.hide();
            console.log('窗口已隐藏');
        } catch (error) {
            console.error('隐藏窗口失败:', error);
        }
    };

    return {
        showCloseDialog,
        setShowCloseDialog,
        handleWindowClose,
        handleMinimizeToTray,
        handleExitApp,
        showWindow,
        hideWindow
    };
};
