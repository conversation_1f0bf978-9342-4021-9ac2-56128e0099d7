import React from 'react';
import { TraySettings } from '../components/settings/TraySettings';
import { useTrayManager } from '../hooks/useTrayManager';
import { getCurrentWindow } from '@tauri-apps/api/window';

const TrayTest: React.FC = () => {
    const { handleMinimizeToTray, handleExitApp, showWindow, hideWindow } = useTrayManager();

    return (
        <div className="container mx-auto p-6 space-y-8">
            <div className="text-center">
                <h1 className="text-3xl font-bold text-base-content mb-2">托盘功能测试</h1>
                <p className="text-base-content/60">测试系统托盘的各种功能</p>
            </div>

            {/* 测试按钮 */}
            <div className="card bg-base-100 shadow-xl">
                <div className="card-body">
                    <h2 className="card-title">托盘操作测试</h2>
                    <div className="grid grid-cols-2 gap-4">
                        <button
                            className="btn btn-primary"
                            onClick={handleMinimizeToTray}
                        >
                            最小化到托盘
                        </button>
                        
                        <button
                            className="btn btn-secondary"
                            onClick={showWindow}
                        >
                            显示窗口
                        </button>
                        
                        <button
                            className="btn btn-accent"
                            onClick={hideWindow}
                        >
                            隐藏窗口
                        </button>
                        
                        <button
                            className="btn btn-error"
                            onClick={handleExitApp}
                        >
                            退出应用
                        </button>
                    </div>
                </div>
            </div>

            {/* 托盘设置 */}
            <div className="card bg-base-100 shadow-xl">
                <div className="card-body">
                    <TraySettings />
                </div>
            </div>

            {/* 使用说明 */}
            <div className="card bg-base-100 shadow-xl">
                <div className="card-body">
                    <h2 className="card-title">使用说明</h2>
                    <div className="space-y-4">
                        <div className="alert alert-info">
                            <svg className="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h3 className="font-bold">测试步骤</h3>
                                <ol className="list-decimal list-inside space-y-1 text-sm mt-2">
                                    <li>点击"最小化到托盘"按钮，应用程序应该隐藏到系统托盘</li>
                                    <li>在系统托盘中找到应用程序图标</li>
                                    <li>单击托盘图标，应用程序应该重新显示</li>
                                    <li>尝试关闭应用程序，应该弹出确认对话框</li>
                                    <li>在设置中调整托盘行为，测试不同的配置</li>
                                </ol>
                            </div>
                        </div>

                        <div className="alert alert-warning">
                            <svg className="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h3 className="font-bold">注意事项</h3>
                                <ul className="list-disc list-inside space-y-1 text-sm mt-2">
                                    <li>托盘功能需要在桌面环境中运行</li>
                                    <li>某些Linux发行版可能需要额外配置</li>
                                    <li>macOS可能需要授权应用程序访问系统托盘</li>
                                    <li>设置更改会立即保存到本地存储</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TrayTest;
