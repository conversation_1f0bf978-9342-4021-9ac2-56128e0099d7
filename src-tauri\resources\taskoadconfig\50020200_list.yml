Vir_50020200_List: &Vir_50020200_List
  - {v_oad: "80500000",      item_07: "00500000",          start_pos: 0,        len_07:     5}         #当前月月度组合有功总累计精确电量
  - {v_oad: "80600000",      item_07: "0060FF00",          start_pos: 0,        len_07:    25}         #精准正向有功数据块
  - {v_oad: "80600100",      item_07: "00600000",          start_pos: 0,        len_07:     5}         #精准正向有功总
  - {v_oad: "80600200",      item_07: "00600100",          start_pos: 0,        len_07:     5}         #精准正向有功费率1
  - {v_oad: "80600300",      item_07: "00600200",          start_pos: 0,        len_07:     5}         #精准正向有功费率2
  - {v_oad: "80600400",      item_07: "00600300",          start_pos: 0,        len_07:     5}         #精准正向有功费率3
  - {v_oad: "80600500",      item_07: "00600400",          start_pos: 0,        len_07:     5}         #精准正向有功费率4
  - {v_oad: "80610000",      item_07: "0061FF00",          start_pos: 0,        len_07:    25}         #精准反向有功数据块
  - {v_oad: "80610100",      item_07: "00610000",          start_pos: 0,        len_07:     5}         #精准反向有功总
  - {v_oad: "80610200",      item_07: "00610100",          start_pos: 0,        len_07:     5}         #精准反向有功费率1
  - {v_oad: "80610300",      item_07: "00610200",          start_pos: 0,        len_07:     5}         #精准反向有功费率2
  - {v_oad: "80610400",      item_07: "00610300",          start_pos: 0,        len_07:     5}         #精准反向有功费率3
  - {v_oad: "80610500",      item_07: "00610400",          start_pos: 0,        len_07:     5}         #精准反向有功费率4
  - {v_oad: "80620000",      item_07: "0062FF00",          start_pos: 0,        len_07:    25}         #精准无功１数据块
  - {v_oad: "80620100",      item_07: "00620000",          start_pos: 0,        len_07:     5}         #精准无功１总
  - {v_oad: "80620200",      item_07: "00620100",          start_pos: 0,        len_07:     5}         #精准无功１费率1
  - {v_oad: "80620300",      item_07: "00620200",          start_pos: 0,        len_07:     5}         #精准无功１费率2
  - {v_oad: "80620400",      item_07: "00620300",          start_pos: 0,        len_07:     5}         #精准无功１费率3
  - {v_oad: "80620500",      item_07: "00620400",          start_pos: 0,        len_07:     5}         #精准无功１费率4
  - {v_oad: "80630000",      item_07: "0063FF00",          start_pos: 0,        len_07:    25}         #精准无功２数据块
  - {v_oad: "80630100",      item_07: "00630000",          start_pos: 0,        len_07:     5}         #精准无功2总
  - {v_oad: "80630200",      item_07: "00630100",          start_pos: 0,        len_07:     5}         #精准无功2费率1
  - {v_oad: "80630300",      item_07: "00630200",          start_pos: 0,        len_07:     5}         #精准无功2费率2
  - {v_oad: "80630400",      item_07: "00630300",          start_pos: 0,        len_07:     5}         #精准无功2费率3
  - {v_oad: "80630500",      item_07: "00630400",          start_pos: 0,        len_07:     5}         #精准无功2费率4
  - {v_oad: "80640000",      item_07: "0064FF00",          start_pos: 0,        len_07:    25}         #精准第一象限无功数据块
  - {v_oad: "80640100",      item_07: "00640000",          start_pos: 0,        len_07:     5}         #精准第一象限无功总
  - {v_oad: "80640200",      item_07: "00640100",          start_pos: 0,        len_07:     5}         #精准第一象限无功费率1
  - {v_oad: "80640300",      item_07: "00640200",          start_pos: 0,        len_07:     5}         #精准第一象限无功费率2
  - {v_oad: "80640400",      item_07: "00640300",          start_pos: 0,        len_07:     5}         #精准第一象限无功费率3
  - {v_oad: "80640500",      item_07: "00640400",          start_pos: 0,        len_07:     5}         #精准第一象限无功费率4
  - {v_oad: "80650000",      item_07: "0065FF00",          start_pos: 0,        len_07:    25}         #精准第二象限无功数据块
  - {v_oad: "80650100",      item_07: "00650000",          start_pos: 0,        len_07:     5}         #精准第二象限无功总
  - {v_oad: "80650200",      item_07: "00650100",          start_pos: 0,        len_07:     5}         #精准第二象限无功费率1
  - {v_oad: "80650300",      item_07: "00650200",          start_pos: 0,        len_07:     5}         #精准第二象限无功费率2
  - {v_oad: "80650400",      item_07: "00650300",          start_pos: 0,        len_07:     5}         #精准第二象限无功费率3
  - {v_oad: "80650500",      item_07: "00650400",          start_pos: 0,        len_07:     5}         #精准第二象限无功费率4
  - {v_oad: "80660000",      item_07: "0066FF00",          start_pos: 0,        len_07:    25}         #精准第三象限无功数据块
  - {v_oad: "80660100",      item_07: "00660000",          start_pos: 0,        len_07:     5}         #精准第三象限无功总
  - {v_oad: "80660200",      item_07: "00660100",          start_pos: 0,        len_07:     5}         #精准第三象限无功费率1
  - {v_oad: "80660300",      item_07: "00660200",          start_pos: 0,        len_07:     5}         #精准第三象限无功费率2
  - {v_oad: "80660400",      item_07: "00660300",          start_pos: 0,        len_07:     5}         #精准第三象限无功费率3
  - {v_oad: "80660500",      item_07: "00660400",          start_pos: 0,        len_07:     5}         #精准第三象限无功费率4
  - {v_oad: "80670000",      item_07: "0067FF00",          start_pos: 0,        len_07:    25}         #精准第四象限无功数据块
  - {v_oad: "80670100",      item_07: "00670000",          start_pos: 0,        len_07:     5}         #精准第四象限无功总
  - {v_oad: "80670200",      item_07: "00670100",          start_pos: 0,        len_07:     5}         #精准第四象限无功费率1
  - {v_oad: "80670300",      item_07: "00670200",          start_pos: 0,        len_07:     5}         #精准第四象限无功费率2
  - {v_oad: "80670400",      item_07: "00670300",          start_pos: 0,        len_07:     5}         #精准第四象限无功费率3
  - {v_oad: "80670500",      item_07: "00670400",          start_pos: 0,        len_07:     5}         #精准第四象限无功费率4
  - {v_oad: "80680000",      item_07: "0068FF00",          start_pos: 0,        len_07:    25}         #精准组合有功数据块
  - {v_oad: "80680100",      item_07: "00680000",          start_pos: 0,        len_07:     5}         #精准组合有功总
  - {v_oad: "80680200",      item_07: "00680100",          start_pos: 0,        len_07:     5}         #精准组合有功费率1
  - {v_oad: "80680300",      item_07: "00680200",          start_pos: 0,        len_07:     5}         #精准组合有功费率2
  - {v_oad: "80680400",      item_07: "00680300",          start_pos: 0,        len_07:     5}         #精准组合有功费率3
  - {v_oad: "80680500",      item_07: "00680400",          start_pos: 0,        len_07:     5}         #精准组合有功费率4
  - {v_oad: "80710000",      item_07: "00710000",          start_pos: 0,        len_07:     5}         #(当前)正向有功总精确电能
  - {v_oad: "80720000",      item_07: "00720000",          start_pos: 0,        len_07:     5}         #(当前)反向有功总精确电能
  - {v_oad: "80750000",      item_07: "00750000",          start_pos: 0,        len_07:     5}         #(当前)第一象限无功总精确电能
  - {v_oad: "80760000",      item_07: "00760000",          start_pos: 0,        len_07:     5}         #(当前)第二象限无功总精确电能
  - {v_oad: "80770000",      item_07: "00770000",          start_pos: 0,        len_07:     5}         #(当前)第三象限无功总精确电能
  - {v_oad: "80780000",      item_07: "00780000",          start_pos: 0,        len_07:     5}         #(当前)第四象限无功总精确电能

  - {v_oad: "86100100",      item_07: "061001FF",          start_pos: 0,        len_07:     6}         #电压
  - {v_oad: "86100101",      item_07: "06100101",          start_pos: 0,        len_07:     2}         #A相电压
  - {v_oad: "86100102",      item_07: "06100102",          start_pos: 0,        len_07:     2}         #B相电压
  - {v_oad: "86100103",      item_07: "06100103",          start_pos: 0,        len_07:     2}         #C相电压

  - {v_oad: "86100200",      item_07: "061002FF",          start_pos: 0,        len_07:     9}         #电流
  - {v_oad: "86100201",      item_07: "06100201",          start_pos: 0,        len_07:     3}         #A相电流
  - {v_oad: "86100202",      item_07: "06100202",          start_pos: 0,        len_07:     3}         #B相电流
  - {v_oad: "86100203",      item_07: "06100203",          start_pos: 0,        len_07:     3}         #C相电流

  - {v_oad: "86100300",      item_07: "061003FF",          start_pos: 0,        len_07:    12}         #有功功率
  - {v_oad: "86100301",      item_07: "06100300",          start_pos: 0,        len_07:     3}         #总有功功率
  - {v_oad: "86100302",      item_07: "06100301",          start_pos: 0,        len_07:     3}         #A相有功功率
  - {v_oad: "86100303",      item_07: "06100302",          start_pos: 0,        len_07:     3}         #B相有功功率
  - {v_oad: "86100304",      item_07: "06100303",          start_pos: 0,        len_07:     3}         #C相有功功率

  - {v_oad: "86100400",      item_07: "061004FF",          start_pos: 0,        len_07:    12}         #无功功率
  - {v_oad: "86100401",      item_07: "06100400",          start_pos: 0,        len_07:     3}         #总无功功率
  - {v_oad: "86100402",      item_07: "06100401",          start_pos: 0,        len_07:     3}         #A相无功功率
  - {v_oad: "86100403",      item_07: "06100402",          start_pos: 0,        len_07:     3}         #B相无功功率
  - {v_oad: "86100404",      item_07: "06100403",          start_pos: 0,        len_07:     3}         #C相无功功率

  - {v_oad: "86100500",      item_07: "061005FF",          start_pos: 0,        len_07:     8}         #功率因数
  - {v_oad: "86100501",      item_07: "06100500",          start_pos: 0,        len_07:     2}         #总功率因数
  - {v_oad: "86100502",      item_07: "06100501",          start_pos: 0,        len_07:     2}         #A相功率因数
  - {v_oad: "86100503",      item_07: "06100502",          start_pos: 0,        len_07:     2}         #B相功率因数
  - {v_oad: "86100504",      item_07: "06100503",          start_pos: 0,        len_07:     2}         #C相功率因数

  - {v_oad: "86100600",      item_07: "061006FF",          start_pos: 0,        len_07:    16}         #有功、无功曲线总电能总数据块
  - {v_oad: "86100601",      item_07: "06100601",          start_pos: 0,        len_07:     4}         #正向有功总电能
  - {v_oad: "86100602",      item_07: "06100602",          start_pos: 0,        len_07:     4}         #反向有功总电能
  - {v_oad: "86100603",      item_07: "06100603",          start_pos: 0,        len_07:     4}         #组合无功 1 总电能
  - {v_oad: "86100604",      item_07: "06100604",          start_pos: 0,        len_07:     4}         #组合无功 2 总电能

  - {v_oad: "86100700",      item_07: "061007FF",          start_pos: 0,        len_07:    16}         #四象限无功曲线数据块
  - {v_oad: "86100701",      item_07: "06100701",          start_pos: 0,        len_07:     4}         #第一象限无功总电能
  - {v_oad: "86100702",      item_07: "06100702",          start_pos: 0,        len_07:     4}         #第二象限无功总电能
  - {v_oad: "86100703",      item_07: "06100703",          start_pos: 0,        len_07:     4}         #第三象限无功总电能
  - {v_oad: "86100704",      item_07: "06100704",          start_pos: 0,        len_07:     4}         #第四象限无功总电能
 
  - {v_oad: "86100800",      item_07: "061008FF",          start_pos: 0,        len_07:     6}         #当前需量曲线数据块
  - {v_oad: "86100801",      item_07: "06100801",          start_pos: 0,        len_07:     3}         #当前有功需量
  - {v_oad: "86100802",      item_07: "06100802",          start_pos: 0,        len_07:     3}         #当前无功需量

  - {v_oad: "86E00001",      item_07: "06E00001",          start_pos: 0,        len_07:    48}         #南网IR46电表新增：曲线第1数据块

  - {v_oad: "8201FF00",      item_07: "0201FF00",          start_pos: 0,        len_07:     6}         #当前电压
  - {v_oad: "8202FF00",      item_07: "0202FF00",          start_pos: 0,        len_07:     9}         #当前电流
  - {v_oad: "8203FF00",      item_07: "0203FF00",          start_pos: 0,        len_07:     12}         #当前有功功率

  - {v_oad: "8001FF00",      item_07: "0001FF00",          start_pos: 0,        len_07:     20}         #当前正向有功总电能

  - {v_oad: "8206FF00",      item_07: "0206FF00",          start_pos: 0,        len_07:     8}         #当前总功率因数

  - {v_oad: "E0030200",      item_07: "020FFF00",          start_pos: 0,        len_07:    122}         #当前交采相角

  - {v_oad: "86100C00",      item_07: "06100CFF",          start_pos: 0,        len_07:    20}         #21规范-组合有功费率1~4电能数据块
  - {v_oad: "86100C01",      item_07: "06100C01",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率1电能
  - {v_oad: "86100C02",      item_07: "06100C02",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率2电能
  - {v_oad: "86100C03",      item_07: "06100C03",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率3电能
  - {v_oad: "86100C04",      item_07: "06100C04",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率4电能
 
  - {v_oad: "86100D00",      item_07: "06100DFF",          start_pos: 0,        len_07:    20}         #21规范-组合有功费率5~8电能数据块
  - {v_oad: "86100D01",      item_07: "06100D01",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率5电能
  - {v_oad: "86100D02",      item_07: "06100D02",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率6电能
  - {v_oad: "86100D03",      item_07: "06100D03",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率7电能
  - {v_oad: "86100D04",      item_07: "06100D04",          start_pos: 0,        len_07:     5}         #21规范-组合有功费率8电能

  - {v_oad: "86100E00",      item_07: "06100EFF",          start_pos: 0,        len_07:    20}         #组合有功费率9～12 电能曲线数据块
  - {v_oad: "86100E01",      item_07: "06100E01",          start_pos: 0,        len_07:     5}         #组合有功费率 9 电能
  - {v_oad: "86100E02",      item_07: "06100E02",          start_pos: 0,        len_07:     5}         #组合有功费率 10 电能
  - {v_oad: "86100E03",      item_07: "06100E03",          start_pos: 0,        len_07:     5}         #组合有功费率 11 电能
  - {v_oad: "86100E04",      item_07: "06100E04",          start_pos: 0,        len_07:     5}         #组合有功费率 12 电能
 
  - {v_oad: "86103000",      item_07: "061030FF",          start_pos: 0,        len_07:    10}         #21规范-组合无功总电能曲线数据块
  - {v_oad: "86103001",      item_07: "06103001",          start_pos: 0,        len_07:     5}         #21规范-组合无功1总电能
  - {v_oad: "86103002",      item_07: "06103002",          start_pos: 0,        len_07:     5}         #21规范-组合无功2总电能

  - {v_oad: "86110600",      item_07: "061106FF",          start_pos: 0,        len_07:    10}         #21规范-有功曲线总电能数据块
  - {v_oad: "86110601",      item_07: "06110601",          start_pos: 0,        len_07:     5}         #21规范-正向有功总电能
  - {v_oad: "86110602",      item_07: "06110602",          start_pos: 0,        len_07:     5}         #21规范-反向有功总电能

  - {v_oad: "86110700",      item_07: "061107FF",          start_pos: 0,        len_07:    20}         #21规范-四象限无功曲线数据块
  - {v_oad: "86110701",      item_07: "06110701",          start_pos: 0,        len_07:     5}         #21规范-第一象限无功总电能
  - {v_oad: "86110702",      item_07: "06110702",          start_pos: 0,        len_07:     5}         #21规范-第二象限无功总电能
  - {v_oad: "86110703",      item_07: "06110703",          start_pos: 0,        len_07:     5}         #21规范-第三象限无功总电能
  - {v_oad: "86110704",      item_07: "06110704",          start_pos: 0,        len_07:     5}         #21规范-第四象限无功总电能

  - {v_oad: "86110900",      item_07: "061109FF",          start_pos: 0,        len_07:    10}         #21规范-谐波总电能数据块数据块
  - {v_oad: "86110901",      item_07: "06110901",          start_pos: 0,        len_07:     5}         #21规范-正向有功谐波总电能
  - {v_oad: "86110902",      item_07: "06110902",          start_pos: 0,        len_07:     5}         #21规范-反向有功谐波总电能
 
  - {v_oad: "86110A00",      item_07: "06110AFF",          start_pos: 0,        len_07:    10}         #21规范-基波总电能数据块数据块
  - {v_oad: "86110A01",      item_07: "06110A01",          start_pos: 0,        len_07:     5}         #21规范-正向有功基波总电能
  - {v_oad: "86110A02",      item_07: "06110A02",          start_pos: 0,        len_07:     5}         #21规范-反向有功基波总电能
 
  - {v_oad: "86120100",      item_07: "061201FF",          start_pos: 0,        len_07:     6}         #模块负荷记录-电压数据块
  - {v_oad: "86120101",      item_07: "06120101",          start_pos: 0,        len_07:     2}         #模块负荷记录-A相电压
  - {v_oad: "86120102",      item_07: "06120102",          start_pos: 0,        len_07:     2}         #模块负荷记录-B相电压
  - {v_oad: "86120103",      item_07: "06120103",          start_pos: 0,        len_07:     2}         #模块负荷记录-C相电压

  - {v_oad: "86120200",      item_07: "061202FF",          start_pos: 0,        len_07:     9}         #模块负荷记录-电流数据块
  - {v_oad: "86120201",      item_07: "06120201",          start_pos: 0,        len_07:     3}         #模块负荷记录-A相电流
  - {v_oad: "86120202",      item_07: "06120202",          start_pos: 0,        len_07:     3}         #模块负荷记录-B相电流
  - {v_oad: "86120203",      item_07: "06120203",          start_pos: 0,        len_07:     3}         #模块负荷记录-C相电流

  - {v_oad: "86120300",      item_07: "061203FF",          start_pos: 0,        len_07:    12}         #模块负荷记录-有功功率数据块
  - {v_oad: "86120301",      item_07: "06120301",          start_pos: 0,        len_07:     3}         #模块负荷记录-总有功功率
  - {v_oad: "86120302",      item_07: "06120302",          start_pos: 0,        len_07:     3}         #模块负荷记录-A相有功功率
  - {v_oad: "86120303",      item_07: "06120303",          start_pos: 0,        len_07:     3}         #模块负荷记录-B相有功功率
  - {v_oad: "86120304",      item_07: "06120304",          start_pos: 0,        len_07:     3}         #模块负荷记录-C相有功功率

  - {v_oad: "86120400",      item_07: "061204FF",          start_pos: 0,        len_07:    12}         #模块负荷记录-无功功率数据块
  - {v_oad: "86120401",      item_07: "06120401",          start_pos: 0,        len_07:     3}         #模块负荷记录-总无功功率
  - {v_oad: "86120402",      item_07: "06120402",          start_pos: 0,        len_07:     3}         #模块负荷记录-A相无功功率
  - {v_oad: "86120403",      item_07: "06120403",          start_pos: 0,        len_07:     3}         #模块负荷记录-B相无功功率
  - {v_oad: "86120404",      item_07: "06120404",          start_pos: 0,        len_07:     3}         #模块负荷记录-C相无功功率

  - {v_oad: "86120500",      item_07: "061205FF",          start_pos: 0,        len_07:     8}         #模块负荷记录-功率因数数据块
  - {v_oad: "86120501",      item_07: "06120501",          start_pos: 0,        len_07:     2}         #模块负荷记录-总功率因数
  - {v_oad: "86120502",      item_07: "06120502",          start_pos: 0,        len_07:     2}         #模块负荷记录-A相功率因数
  - {v_oad: "86120503",      item_07: "06120503",          start_pos: 0,        len_07:     2}         #模块负荷记录-B相功率因数
  - {v_oad: "86120504",      item_07: "06120504",          start_pos: 0,        len_07:     2}         #模块负荷记录-C相功率因数

  - {v_oad: "86120600",      item_07: "061206FF",          start_pos: 0,        len_07:    16}         #模块负荷记录-有功，无功总电能数据块
  - {v_oad: "86120601",      item_07: "06120601",          start_pos: 0,        len_07:     4}         #模块负荷记录-正向有功总电能
  - {v_oad: "86120602",      item_07: "06120602",          start_pos: 0,        len_07:     4}         #模块负荷记录-反向有功总电能
  - {v_oad: "86120603",      item_07: "06120603",          start_pos: 0,        len_07:     4}         #模块负荷记录-组合无功1总电能
  - {v_oad: "86120604",      item_07: "06120604",          start_pos: 0,        len_07:     4}         #模块负荷记录-组合无功2总电能

  - {v_oad: "86120700",      item_07: "061207FF",          start_pos: 0,        len_07:    16}         #模块负荷记录-四象限无功总电能数据块
  - {v_oad: "86120701",      item_07: "06120701",          start_pos: 0,        len_07:     4}         #模块负荷记录-第一象限无功总电能
  - {v_oad: "86120702",      item_07: "06120702",          start_pos: 0,        len_07:     4}         #模块负荷记录-第二象限无功总电能
  - {v_oad: "86120703",      item_07: "06120703",          start_pos: 0,        len_07:     4}         #模块负荷记录-第三象限无功总电能
  - {v_oad: "86120704",      item_07: "06120704",          start_pos: 0,        len_07:     4}         #模块负荷记录-第四象限无功总电能

  - {v_oad: "86120800",      item_07: "061208FF",          start_pos: 0,        len_07:     6}         #模块负荷记录-当前需量数据块
  - {v_oad: "86120801",      item_07: "06120801",          start_pos: 0,        len_07:     3}         #模块负荷记录-当前有功需量
  - {v_oad: "86120802",      item_07: "06120802",          start_pos: 0,        len_07:     3}         #模块负荷记录-当前无功需量

  - {v_oad: "8701FF00",      item_07: "0701FF00",          start_pos: 0,        len_07:    87}

  - {v_oad: "84000101",      item_07: "04000101",          start_pos: 0,        len_07:     4}         #电能表日期及星期
  - {v_oad: "84000102",      item_07: "04000102",          start_pos: 0,        len_07:     3}         #电能表时间

LOAD_CUR_ITEM: &LOAD_CUR_ITEM
  - {v_oad: "00000200",   item_07: "0000FF00",   start_pos:   0,        len_07:     20    }  #组合有功
  - {v_oad: "00000201",   item_07: "00000000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00000202",   item_07: "00000100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00000203",   item_07: "00000200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00000204",   item_07: "00000300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00000205",   item_07: "00000400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00100200",   item_07: "0001FF00",   start_pos:   0,        len_07:     20    }  #正向有功
  - {v_oad: "00100201",   item_07: "00010000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00100202",   item_07: "00010100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00100203",   item_07: "00010200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00100204",   item_07: "00010300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00100205",   item_07: "00010400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00100400",   item_07: "006000FF",   start_pos:   0,        len_07:     25    }  #正向有功高精度
  - {v_oad: "00100401",   item_07: "00600001",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00100402",   item_07: "00600002",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00100403",   item_07: "00600003",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00100404",   item_07: "00600004",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00100405",   item_07: "00600005",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00110200",   item_07: "00150000",   start_pos:   0,        len_07:      4    }  #A相正向有功电能
  - {v_oad: "00110201",   item_07: "00150000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00120200",   item_07: "00290000",   start_pos:   0,        len_07:      4    }  #B相正向有功电能
  - {v_oad: "00120201",   item_07: "00290000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00130200",   item_07: "003D0000",   start_pos:   0,        len_07:      4    }  #C相正向有功电能
  - {v_oad: "00130201",   item_07: "003D0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00200200",   item_07: "0002FF00",   start_pos:   0,        len_07:     20    }  #反向有功
  - {v_oad: "00200201",   item_07: "00020000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00200202",   item_07: "00020100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00200203",   item_07: "00020200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00200204",   item_07: "00020300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00200205",   item_07: "00020400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00200400",   item_07: "006100FF",   start_pos:   0,        len_07:     25    }  #反向有功高精度
  - {v_oad: "00200401",   item_07: "00610001",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00200402",   item_07: "00610002",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00200403",   item_07: "00610003",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00200404",   item_07: "00610004",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00200405",   item_07: "00610005",   start_pos:   0,        len_07:      5    }
  - {v_oad: "00210200",   item_07: "00160000",   start_pos:   0,        len_07:      4    }  #A相反向有功电能
  - {v_oad: "00210201",   item_07: "00160000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00220200",   item_07: "002A0000",   start_pos:   0,        len_07:      4    }  #B相反向有功电能
  - {v_oad: "00220201",   item_07: "002A0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00230200",   item_07: "003E0000",   start_pos:   0,        len_07:      4    }  #C相反向有功电能
  - {v_oad: "00230201",   item_07: "003E0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00300200",   item_07: "0003FF00",   start_pos:   0,        len_07:     20    }  #组合无功1
  - {v_oad: "00300201",   item_07: "00030000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00300202",   item_07: "00030100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00300203",   item_07: "00030200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00300204",   item_07: "00030300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00300205",   item_07: "00030400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00310200",   item_07: "00170000",   start_pos:   0,        len_07:      4    }  #A相组合无功1电能
  - {v_oad: "00310201",   item_07: "00170000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00320200",   item_07: "002B0000",   start_pos:   0,        len_07:      4    }  #B相组合无功1电能
  - {v_oad: "00320201",   item_07: "002B0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00330200",   item_07: "003F0000",   start_pos:   0,        len_07:      4    }  #C相组合无功1电能
  - {v_oad: "00330201",   item_07: "003F0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00400200",   item_07: "0004FF00",   start_pos:   0,        len_07:     20    }  #组合无功2
  - {v_oad: "00400201",   item_07: "00040000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00400202",   item_07: "00040100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00400203",   item_07: "00040200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00400204",   item_07: "00040300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00400205",   item_07: "00040400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00410200",   item_07: "00180000",   start_pos:   0,        len_07:      4    }  #A相组合无功2电能
  - {v_oad: "00410201",   item_07: "00180000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00420200",   item_07: "002C0000",   start_pos:   0,        len_07:      4    }  #B相组合无功2电能
  - {v_oad: "00420201",   item_07: "002C0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00430200",   item_07: "00400000",   start_pos:   0,        len_07:      4    }  #C相组合无功2电能
  - {v_oad: "00430201",   item_07: "00400000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00500200",   item_07: "0005FF00",   start_pos:   0,        len_07:     20    }  #第一象限无功电能
  - {v_oad: "00500201",   item_07: "00050000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00500202",   item_07: "00050100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00500203",   item_07: "00050200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00500204",   item_07: "00050300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00500205",   item_07: "00050400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00510200",   item_07: "00190000",   start_pos:   0,        len_07:      4    }  #A相第一象限无功电能
  - {v_oad: "00510201",   item_07: "00190000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00520200",   item_07: "002D0000",   start_pos:   0,        len_07:      4    }  #B相第一象限无功电能
  - {v_oad: "00520201",   item_07: "002D0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00530200",   item_07: "00410000",   start_pos:   0,        len_07:      4    }  #C相第一象限无功电能
  - {v_oad: "00530201",   item_07: "00410000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00600200",   item_07: "0006FF00",   start_pos:   0,        len_07:     20    }  #第二象限无功电能
  - {v_oad: "00600201",   item_07: "00060000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00600202",   item_07: "00060100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00600203",   item_07: "00060200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00600204",   item_07: "00060300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00600205",   item_07: "00060400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00610200",   item_07: "001A0000",   start_pos:   0,        len_07:      4    }  #A相第二象限无功电能
  - {v_oad: "00610201",   item_07: "001A0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00620200",   item_07: "002E0000",   start_pos:   0,        len_07:      4    }  #B相第二象限无功电能
  - {v_oad: "00620201",   item_07: "002E0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00630200",   item_07: "00420000",   start_pos:   0,        len_07:      4    }  #C相第二象限无功电能
  - {v_oad: "00630201",   item_07: "00420000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00700200",   item_07: "0007FF00",   start_pos:   0,        len_07:     20    }  #第三象限无功电能
  - {v_oad: "00700201",   item_07: "00070000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00700202",   item_07: "00070100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00700203",   item_07: "00070200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00700204",   item_07: "00070300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00700205",   item_07: "00070400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00710200",   item_07: "001B0000",   start_pos:   0,        len_07:      4    }  #A相第三象限无功电能
  - {v_oad: "00710201",   item_07: "001B0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00720200",   item_07: "002F0000",   start_pos:   0,        len_07:      4    }  #B相第三象限无功电能
  - {v_oad: "00720201",   item_07: "002F0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00730200",   item_07: "00430000",   start_pos:   0,        len_07:      4    }  #C相第三象限无功电能
  - {v_oad: "00730201",   item_07: "00430000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00800200",   item_07: "0008FF00",   start_pos:   0,        len_07:     20    }  #第四象限无功电能
  - {v_oad: "00800201",   item_07: "00080000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00800202",   item_07: "00080100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00800203",   item_07: "00080200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00800204",   item_07: "00080300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00800205",   item_07: "00080400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00810200",   item_07: "001C0000",   start_pos:   0,        len_07:      4    }  #A相第四象限无功电能
  - {v_oad: "00810201",   item_07: "001C0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00820200",   item_07: "00300000",   start_pos:   0,        len_07:      4    }  #B相第四象限无功电能
  - {v_oad: "00820201",   item_07: "00300000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00830200",   item_07: "00440000",   start_pos:   0,        len_07:      4    }  #C相第四象限无功电能
  - {v_oad: "00830201",   item_07: "00440000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00900200",   item_07: "0009FF00",   start_pos:   0,        len_07:     20    }  #正向视在电能
  - {v_oad: "00900201",   item_07: "00090000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00900202",   item_07: "00090100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00900203",   item_07: "00090200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00900204",   item_07: "00090300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00900205",   item_07: "00090400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00910200",   item_07: "001D0000",   start_pos:   0,        len_07:      4    }  #A相正向视在电能
  - {v_oad: "00910201",   item_07: "001D0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00920200",   item_07: "00310000",   start_pos:   0,        len_07:      4    }  #B相正向视在电能
  - {v_oad: "00920201",   item_07: "00310000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00930200",   item_07: "00450000",   start_pos:   0,        len_07:      4    }  #C相正向视在电能
  - {v_oad: "00930201",   item_07: "00450000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A00200",   item_07: "000AFF00",   start_pos:   0,        len_07:     20    }  #反向视在电能
  - {v_oad: "00A00201",   item_07: "000A0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A00202",   item_07: "000A0100",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A00203",   item_07: "000A0200",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A00204",   item_07: "000A0300",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A00205",   item_07: "000A0400",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A10200",   item_07: "001E0000",   start_pos:   0,        len_07:      4    }  #A相正向视在电能
  - {v_oad: "00A10201",   item_07: "001E0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A20200",   item_07: "00320000",   start_pos:   0,        len_07:      4    }  #B相正向视在电能
  - {v_oad: "00A20201",   item_07: "00320000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "00A30200",   item_07: "00460000",   start_pos:   0,        len_07:      4    }  #C相正向视在电能
  - {v_oad: "00A30201",   item_07: "00460000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "01100200",   item_07: "00810000",   start_pos:   0,        len_07:      4    }  #正向有功基波总电能
  - {v_oad: "01100201",   item_07: "00810000",   start_pos:   0,        len_07:      4    }  #正向有功基波总电能
  - {v_oad: "01110200",   item_07: "00950000",   start_pos:   0,        len_07:      4    }  #A相正向有功基波总电能
  - {v_oad: "01110201",   item_07: "00950000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "01120200",   item_07: "00A90000",   start_pos:   0,        len_07:      4    }  #B相正向有功基波总电能
  - {v_oad: "01120201",   item_07: "00A90000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "01130200",   item_07: "00BD0000",   start_pos:   0,        len_07:      4    }  #C相正向有功基波总电能
  - {v_oad: "01130201",   item_07: "00BD0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "01200200",   item_07: "00820000",   start_pos:   0,        len_07:      4    }  #反向有功基波总电能
  - {v_oad: "01200201",   item_07: "00820000",   start_pos:   0,        len_07:      4    }  #反向有功基波总电能
  - {v_oad: "01210200",   item_07: "00960000",   start_pos:   0,        len_07:      4    }  #A相反向有功基波总电能
  - {v_oad: "01210201",   item_07: "00960000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "01220200",   item_07: "00AA0000",   start_pos:   0,        len_07:      4    }  #B相反向有功基波总电能
  - {v_oad: "01220201",   item_07: "00AA0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "01230200",   item_07: "00BE0000",   start_pos:   0,        len_07:      4    }  #C相反向有功基波总电能
  - {v_oad: "01230201",   item_07: "00BE0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "02100200",   item_07: "00830000",   start_pos:   0,        len_07:      4    }  #正向有功谐波总电能
  - {v_oad: "02100201",   item_07: "00830000",   start_pos:   0,        len_07:      4    }  #正向有功谐波总电能
  - {v_oad: "02110200",   item_07: "00970000",   start_pos:   0,        len_07:      4    }  #A相正向有功谐波总电能
  - {v_oad: "02110201",   item_07: "00970000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "02120200",   item_07: "00AB0000",   start_pos:   0,        len_07:      4    }  #B相正向有功谐波总电能
  - {v_oad: "02120201",   item_07: "00AB0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "02130200",   item_07: "00BF0000",   start_pos:   0,        len_07:      4    }  #C相正向有功谐波总电能
  - {v_oad: "02130201",   item_07: "00BF0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "02200200",   item_07: "00840000",   start_pos:   0,        len_07:      4    }  #反向有功谐波总电能
  - {v_oad: "02200201",   item_07: "00840000",   start_pos:   0,        len_07:      4    }  #反向有功谐波总电能
  - {v_oad: "02210200",   item_07: "00980000",   start_pos:   0,        len_07:      4    }  #A相反向有功谐波总电能
  - {v_oad: "02210201",   item_07: "00980000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "02220200",   item_07: "00AC0000",   start_pos:   0,        len_07:      4    }  #B相反向有功谐波总电能
  - {v_oad: "02220201",   item_07: "00AC0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "02230200",   item_07: "00C00000",   start_pos:   0,        len_07:      4    }  #C相反向有功谐波总电能
  - {v_oad: "02230201",   item_07: "00C00000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "03000200",   item_07: "00850000",   start_pos:   0,        len_07:      4    }  #铜损有功总电能补偿量
  - {v_oad: "03000201",   item_07: "00850000",   start_pos:   0,        len_07:      4    }  #铜损有功总电能补偿量
  - {v_oad: "03010200",   item_07: "00990000",   start_pos:   0,        len_07:      4    }  #A相铜损有功总电能补偿量
  - {v_oad: "03010201",   item_07: "00990000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "03020200",   item_07: "00AD0000",   start_pos:   0,        len_07:      4    }  #B相铜损有功总电能补偿量
  - {v_oad: "03020201",   item_07: "00AD0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "03030200",   item_07: "00C10000",   start_pos:   0,        len_07:      4    }  #C相铜损有功总电能补偿量
  - {v_oad: "03030201",   item_07: "00C10000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "04000200",   item_07: "00860000",   start_pos:   0,        len_07:      4    }  #铁损有功总电能补偿量
  - {v_oad: "04000201",   item_07: "00860000",   start_pos:   0,        len_07:      4    }  #铁损有功总电能补偿量
  - {v_oad: "04010200",   item_07: "009A0000",   start_pos:   0,        len_07:      4    }  #A相铁损有功总电能补偿量
  - {v_oad: "04010201",   item_07: "009A0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "04020200",   item_07: "00AE0000",   start_pos:   0,        len_07:      4    }  #B相铁损有功总电能补偿量
  - {v_oad: "04020201",   item_07: "00AE0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "04030200",   item_07: "00C20000",   start_pos:   0,        len_07:      4    }  #C相铁损有功总电能补偿量
  - {v_oad: "04030201",   item_07: "00C20000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "05000200",   item_07: "00800000",   start_pos:   0,        len_07:      4    }  #关联总电能
  - {v_oad: "05000201",   item_07: "00800000",   start_pos:   0,        len_07:      4    }  #关联总电能
  - {v_oad: "05010200",   item_07: "00940000",   start_pos:   0,        len_07:      4    }  #A相关联总电能
  - {v_oad: "05010201",   item_07: "00940000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "05020200",   item_07: "00A80000",   start_pos:   0,        len_07:      4    }  #B相关联总电能
  - {v_oad: "05020201",   item_07: "00A80000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "05030200",   item_07: "00BC0000",   start_pos:   0,        len_07:      4    }  #C相关联总电能
  - {v_oad: "05030201",   item_07: "00BC0000",   start_pos:   0,        len_07:      4    }
  - {v_oad: "10100200",   item_07: "0101FF00",   start_pos:   0,        len_07:     40    }  #正向有功最大需量
  - {v_oad: "10100201",   item_07: "01010000",   start_pos:   0,        len_07:      8    }  #正向有功总最大需量及发生时间
  - {v_oad: "10100202",   item_07: "01010100",   start_pos:   0,        len_07:      8    }  #正向有功费率1 最大需量及发生时间
  - {v_oad: "10100203",   item_07: "01010200",   start_pos:   0,        len_07:      8    }  #正向有功费率2 最大需量及发生时间
  - {v_oad: "10100204",   item_07: "01010300",   start_pos:   0,        len_07:      8    }  #正向有功费率3 最大需量及发生时间
  - {v_oad: "10100205",   item_07: "01010400",   start_pos:   0,        len_07:      8    }  #正向有功费率4 最大需量及发生时间
  - {v_oad: "10110200",   item_07: "01150000",   start_pos:   0,        len_07:      8    }  #A相正向有功最大需量
  - {v_oad: "10110201",   item_07: "01150000",   start_pos:   0,        len_07:      8    }  #A相正向有功最大需量
  - {v_oad: "10120200",   item_07: "01290000",   start_pos:   0,        len_07:      8    }  #B相正向有功最大需量
  - {v_oad: "10120201",   item_07: "01290000",   start_pos:   0,        len_07:      8    }  #B相正向有功最大需量
  - {v_oad: "10130200",   item_07: "013D0000",   start_pos:   0,        len_07:      8    }  #C相正向有功最大需量
  - {v_oad: "10130201",   item_07: "013D0000",   start_pos:   0,        len_07:      8    }  #C相正向有功最大需量
  - {v_oad: "10200200",   item_07: "0102FF00",   start_pos:   0,        len_07:     40    }  #反向有功最大需量
  - {v_oad: "10200201",   item_07: "01020000",   start_pos:   0,        len_07:      8    }  #反向有功总最大需量及发生时间
  - {v_oad: "10200202",   item_07: "01020100",   start_pos:   0,        len_07:      8    }  #反向有功费率1 最大需量及发生时间
  - {v_oad: "10200203",   item_07: "01020200",   start_pos:   0,        len_07:      8    }  #反向有功费率2 最大需量及发生时间
  - {v_oad: "10200204",   item_07: "01020300",   start_pos:   0,        len_07:      8    }  #反向有功费率3 最大需量及发生时间
  - {v_oad: "10200205",   item_07: "01020400",   start_pos:   0,        len_07:      8    }  #反向有功费率4 最大需量及发生时间
  - {v_oad: "10210200",   item_07: "01160000",   start_pos:   0,        len_07:      8    }  #A相反向有功最大需量
  - {v_oad: "10210201",   item_07: "01160000",   start_pos:   0,        len_07:      8    }  #A相反向有功最大需量
  - {v_oad: "10220200",   item_07: "012A0000",   start_pos:   0,        len_07:      8    }  #B相反向有功最大需量
  - {v_oad: "10220201",   item_07: "012A0000",   start_pos:   0,        len_07:      8    }  #B相反向有功最大需量
  - {v_oad: "10230200",   item_07: "013E0000",   start_pos:   0,        len_07:      8    }  #C相反向有功最大需量
  - {v_oad: "10230201",   item_07: "013E0000",   start_pos:   0,        len_07:      8    }  #C相反向有功最大需量
  - {v_oad: "10300200",   item_07: "0103FF00",   start_pos:   0,        len_07:     40    }  #组合无功1最大需量
  - {v_oad: "10300201",   item_07: "01030000",   start_pos:   0,        len_07:      8    }  #组合无功1最大需量及发生时间
  - {v_oad: "10300202",   item_07: "01030100",   start_pos:   0,        len_07:      8    }  #组合无功1费率1 最大需量及发生时间
  - {v_oad: "10300203",   item_07: "01030200",   start_pos:   0,        len_07:      8    }  #组合无功1费率2 最大需量及发生时间
  - {v_oad: "10300204",   item_07: "01030300",   start_pos:   0,        len_07:      8    }  #组合无功1费率3 最大需量及发生时间
  - {v_oad: "10300205",   item_07: "01030400",   start_pos:   0,        len_07:      8    }  #组合无功1费率4 最大需量及发生时间
  - {v_oad: "10310200",   item_07: "01170000",   start_pos:   0,        len_07:      8    }  #A相组合无功1最大需量
  - {v_oad: "10310201",   item_07: "01170000",   start_pos:   0,        len_07:      8    }  #A相组合无功1最大需量
  - {v_oad: "10320200",   item_07: "012B0000",   start_pos:   0,        len_07:      8    }  #B相组合无功1最大需量
  - {v_oad: "10320201",   item_07: "012B0000",   start_pos:   0,        len_07:      8    }  #B相组合无功1最大需量
  - {v_oad: "10330200",   item_07: "013F0000",   start_pos:   0,        len_07:      8    }  #C相组合无功1最大需量
  - {v_oad: "10330201",   item_07: "013F0000",   start_pos:   0,        len_07:      8    }  #C相组合无功1最大需量
  - {v_oad: "10400200",   item_07: "0104FF00",   start_pos:   0,        len_07:     40    }  #组合无功2最大需量
  - {v_oad: "10400201",   item_07: "01040000",   start_pos:   0,        len_07:      8    }  #组合无功2最大需量及发生时间
  - {v_oad: "10400202",   item_07: "01040100",   start_pos:   0,        len_07:      8    }  #组合无功2费率1 最大需量及发生时间
  - {v_oad: "10400203",   item_07: "01040200",   start_pos:   0,        len_07:      8    }  #组合无功2费率2 最大需量及发生时间
  - {v_oad: "10400204",   item_07: "01040300",   start_pos:   0,        len_07:      8    }  #组合无功2费率3 最大需量及发生时间
  - {v_oad: "10400205",   item_07: "01040400",   start_pos:   0,        len_07:      8    }  #组合无功2费率4 最大需量及发生时间
  - {v_oad: "10410200",   item_07: "01180000",   start_pos:   0,        len_07:      8    }  #A相组合无功2最大需量
  - {v_oad: "10410201",   item_07: "01180000",   start_pos:   0,        len_07:      8    }  #A相组合无功2最大需量
  - {v_oad: "10420200",   item_07: "012C0000",   start_pos:   0,        len_07:      8    }  #B相组合无功2最大需量
  - {v_oad: "10420201",   item_07: "012C0000",   start_pos:   0,        len_07:      8    }  #B相组合无功2最大需量
  - {v_oad: "10430200",   item_07: "01400000",   start_pos:   0,        len_07:      8    }  #C相组合无功2最大需量
  - {v_oad: "10430201",   item_07: "01400000",   start_pos:   0,        len_07:      8    }  #C相组合无功2最大需量
  - {v_oad: "10500200",   item_07: "0105FF00",   start_pos:   0,        len_07:     40    }  #第一象限最大需量
  - {v_oad: "10500201",   item_07: "01050000",   start_pos:   0,        len_07:      8    }  #第一象限总最大需量及发生时间
  - {v_oad: "10500202",   item_07: "01050100",   start_pos:   0,        len_07:      8    }  #第一象限费率1 最大需量及发生时间
  - {v_oad: "10500203",   item_07: "01050200",   start_pos:   0,        len_07:      8    }  #第一象限费率2 最大需量及发生时间
  - {v_oad: "10500204",   item_07: "01050300",   start_pos:   0,        len_07:      8    }  #第一象限费率3 最大需量及发生时间
  - {v_oad: "10500205",   item_07: "01050400",   start_pos:   0,        len_07:      8    }  #第一象限费率4 最大需量及发生时间
  - {v_oad: "10510200",   item_07: "01190000",   start_pos:   0,        len_07:      8    }  #A相第一象限最大需量
  - {v_oad: "10510201",   item_07: "01190000",   start_pos:   0,        len_07:      8    }  #A相第一象限最大需量
  - {v_oad: "10520200",   item_07: "012D0000",   start_pos:   0,        len_07:      8    }  #B相第一象限最大需量
  - {v_oad: "10520201",   item_07: "012D0000",   start_pos:   0,        len_07:      8    }  #B相第一象限最大需量
  - {v_oad: "10530200",   item_07: "01410000",   start_pos:   0,        len_07:      8    }  #C相第一象限最大需量
  - {v_oad: "10530201",   item_07: "01410000",   start_pos:   0,        len_07:      8    }  #C相第一象限最大需量
  - {v_oad: "10600200",   item_07: "0106FF00",   start_pos:   0,        len_07:     40    }  #第二象限最大需量
  - {v_oad: "10600201",   item_07: "01060000",   start_pos:   0,        len_07:      8    }  #第二象限总最大需量及发生时间
  - {v_oad: "10600202",   item_07: "01060100",   start_pos:   0,        len_07:      8    }  #第二象限费率1 最大需量及发生时间
  - {v_oad: "10600203",   item_07: "01060200",   start_pos:   0,        len_07:      8    }  #第二象限费率2 最大需量及发生时间
  - {v_oad: "10600204",   item_07: "01060300",   start_pos:   0,        len_07:      8    }  #第二象限费率3 最大需量及发生时间
  - {v_oad: "10600205",   item_07: "01060400",   start_pos:   0,        len_07:      8    }  #第二象限费率4 最大需量及发生时间
  - {v_oad: "10610200",   item_07: "011A0000",   start_pos:   0,        len_07:      8    }  #A相第二象限最大需量
  - {v_oad: "10610201",   item_07: "011A0000",   start_pos:   0,        len_07:      8    }  #A相第二象限最大需量
  - {v_oad: "10620200",   item_07: "012E0000",   start_pos:   0,        len_07:      8    }  #B相第二象限最大需量
  - {v_oad: "10620201",   item_07: "012E0000",   start_pos:   0,        len_07:      8    }  #B相第二象限最大需量
  - {v_oad: "10630200",   item_07: "01420000",   start_pos:   0,        len_07:      8    }  #C相第二象限最大需量
  - {v_oad: "10630201",   item_07: "01420000",   start_pos:   0,        len_07:      8    }  #C相第二象限最大需量
  - {v_oad: "10700200",   item_07: "0107FF00",   start_pos:   0,        len_07:     40    }  #第三象限最大需量
  - {v_oad: "10700201",   item_07: "01070000",   start_pos:   0,        len_07:      8    }  #第三象限总最大需量及发生时间
  - {v_oad: "10700202",   item_07: "01070100",   start_pos:   0,        len_07:      8    }  #第三象限费率1 最大需量及发生时间
  - {v_oad: "10700203",   item_07: "01070200",   start_pos:   0,        len_07:      8    }  #第三象限费率2 最大需量及发生时间
  - {v_oad: "10700204",   item_07: "01070300",   start_pos:   0,        len_07:      8    }  #第三象限费率3 最大需量及发生时间
  - {v_oad: "10700205",   item_07: "01070400",   start_pos:   0,        len_07:      8    }  #第三象限费率4 最大需量及发生时间
  - {v_oad: "10710200",   item_07: "011B0000",   start_pos:   0,        len_07:      8    }  #A相第三象限最大需量
  - {v_oad: "10710201",   item_07: "011B0000",   start_pos:   0,        len_07:      8    }  #A相第三象限最大需量
  - {v_oad: "10720200",   item_07: "012F0000",   start_pos:   0,        len_07:      8    }  #B相第三象限最大需量
  - {v_oad: "10720201",   item_07: "012F0000",   start_pos:   0,        len_07:      8    }  #B相第三象限最大需量
  - {v_oad: "10730200",   item_07: "01430000",   start_pos:   0,        len_07:      8    }  #C相第三象限最大需量
  - {v_oad: "10730201",   item_07: "01430000",   start_pos:   0,        len_07:      8    }  #C相第三象限最大需量
  - {v_oad: "10800200",   item_07: "0108FF00",   start_pos:   0,        len_07:     40    }  #第四象限最大需量
  - {v_oad: "10800201",   item_07: "01080000",   start_pos:   0,        len_07:      8    }  #第四象限总最大需量及发生时间
  - {v_oad: "10800202",   item_07: "01080100",   start_pos:   0,        len_07:      8    }  #第四象限费率1 最大需量及发生时间
  - {v_oad: "10800203",   item_07: "01080200",   start_pos:   0,        len_07:      8    }  #第四象限费率2 最大需量及发生时间
  - {v_oad: "10800204",   item_07: "01080300",   start_pos:   0,        len_07:      8    }  #第四象限费率3 最大需量及发生时间
  - {v_oad: "10800205",   item_07: "01080400",   start_pos:   0,        len_07:      8    }  #第四象限费率4 最大需量及发生时间
  - {v_oad: "10810200",   item_07: "011C0000",   start_pos:   0,        len_07:      8    }  #A相第四象限最大需量
  - {v_oad: "10810201",   item_07: "011C0000",   start_pos:   0,        len_07:      8    }  #A相第四象限最大需量
  - {v_oad: "10820200",   item_07: "01300000",   start_pos:   0,        len_07:      8    }  #B相第四象限最大需量
  - {v_oad: "10820201",   item_07: "01300000",   start_pos:   0,        len_07:      8    }  #B相第四象限最大需量
  - {v_oad: "10830200",   item_07: "01440000",   start_pos:   0,        len_07:      8    }  #C相第四象限最大需量
  - {v_oad: "10830201",   item_07: "01440000",   start_pos:   0,        len_07:      8    }  #C相第四象限最大需量
  - {v_oad: "10900200",   item_07: "0109FF00",   start_pos:   0,        len_07:     40    }  #正向视在最大需量
  - {v_oad: "10900201",   item_07: "01090000",   start_pos:   0,        len_07:      8    }  #正向视在总最大需量及发生时间
  - {v_oad: "10900202",   item_07: "01090100",   start_pos:   0,        len_07:      8    }  #正向视在费率1 最大需量及发生时间
  - {v_oad: "10900203",   item_07: "01090200",   start_pos:   0,        len_07:      8    }  #正向视在费率2 最大需量及发生时间
  - {v_oad: "10900204",   item_07: "01090300",   start_pos:   0,        len_07:      8    }  #正向视在费率3 最大需量及发生时间
  - {v_oad: "10900205",   item_07: "01090400",   start_pos:   0,        len_07:      8    }  #正向视在费率4 最大需量及发生时间
  - {v_oad: "10910200",   item_07: "011D0000",   start_pos:   0,        len_07:      8    }  #A相正向视在最大需量
  - {v_oad: "10910201",   item_07: "011D0000",   start_pos:   0,        len_07:      8    }  #A相正向视在最大需量
  - {v_oad: "10920200",   item_07: "01310000",   start_pos:   0,        len_07:      8    }  #B相正向视在最大需量
  - {v_oad: "10920201",   item_07: "01310000",   start_pos:   0,        len_07:      8    }  #B相正向视在最大需量
  - {v_oad: "10930200",   item_07: "01450000",   start_pos:   0,        len_07:      8    }  #C相正向视在最大需量
  - {v_oad: "10930201",   item_07: "01450000",   start_pos:   0,        len_07:      8    }  #C相正向视在最大需量
  - {v_oad: "10A00200",   item_07: "010AFF00",   start_pos:   0,        len_07:     40    }  #反向视在最大需量
  - {v_oad: "10A00201",   item_07: "010A0000",   start_pos:   0,        len_07:      8    }  #反向视在总最大需量及发生时间
  - {v_oad: "10A00202",   item_07: "010A0100",   start_pos:   0,        len_07:      8    }  #反向视在费率1 最大需量及发生时间
  - {v_oad: "10A00203",   item_07: "010A0200",   start_pos:   0,        len_07:      8    }  #反向视在费率2 最大需量及发生时间
  - {v_oad: "10A00204",   item_07: "010A0300",   start_pos:   0,        len_07:      8    }  #反向视在费率3 最大需量及发生时间
  - {v_oad: "10A00205",   item_07: "010A0400",   start_pos:   0,        len_07:      8    }  #反向视在费率4 最大需量及发生时间
  - {v_oad: "10A10200",   item_07: "011E0000",   start_pos:   0,        len_07:      8    }  #A相反向视在最大需量
  - {v_oad: "10A10201",   item_07: "011E0000",   start_pos:   0,        len_07:      8    }  #A相反向视在最大需量
  - {v_oad: "10A20200",   item_07: "01320000",   start_pos:   0,        len_07:      8    }  #B相反向视在最大需量
  - {v_oad: "10A20201",   item_07: "01320000",   start_pos:   0,        len_07:      8    }  #B相反向视在最大需量
  - {v_oad: "10A30200",   item_07: "01460000",   start_pos:   0,        len_07:      8    }  #C相反向视在最大需量
  - {v_oad: "10A30201",   item_07: "01460000",   start_pos:   0,        len_07:      8    }  #C相反向视在最大需量
  - {v_oad: "20000200",   item_07: "0201FF00",   start_pos:   0,        len_07:      6    }  #电压
  - {v_oad: "20000201",   item_07: "02010100",   start_pos:   0,        len_07:      2    }  #A相电压
  - {v_oad: "20000202",   item_07: "02010200",   start_pos:   0,        len_07:      2    }  #B相电压
  - {v_oad: "20000203",   item_07: "02010300",   start_pos:   0,        len_07:      2    }  #C相电压
  - {v_oad: "20010200",   item_07: "0202FF00",   start_pos:   0,        len_07:      9    }  #电流
  - {v_oad: "20010201",   item_07: "02020100",   start_pos:   0,        len_07:      3    }  #A相电流
  - {v_oad: "20010202",   item_07: "02020200",   start_pos:   0,        len_07:      3    }  #B相电流
  - {v_oad: "20010203",   item_07: "02020300",   start_pos:   0,        len_07:      3    }  #C相电流
  - {v_oad: "20010400",   item_07: "02800001",   start_pos:   0,        len_07:      3    }  #零线电流
  - {v_oad: "20030200",   item_07: "0207FF00",   start_pos:   0,        len_07:      6    }  #电压相角
  - {v_oad: "20030201",   item_07: "02070100",   start_pos:   0,        len_07:      2    }  #A相电压相角
  - {v_oad: "20030202",   item_07: "02070200",   start_pos:   0,        len_07:      2    }  #B相电压相角
  - {v_oad: "20030203",   item_07: "02070300",   start_pos:   0,        len_07:      2    }  #C相电压相角
  - {v_oad: "20040200",   item_07: "0203FF00",   start_pos:   0,        len_07:     12    }  #有功功率
  - {v_oad: "20040201",   item_07: "02030000",   start_pos:   0,        len_07:      3    }  #总有功功率
  - {v_oad: "20040202",   item_07: "02030100",   start_pos:   0,        len_07:      3    }  #A 相有功功率
  - {v_oad: "20040203",   item_07: "02030200",   start_pos:   0,        len_07:      3    }  #B 相有功功率
  - {v_oad: "20040204",   item_07: "02030300",   start_pos:   0,        len_07:      3    }  #C 相有功功率
  - {v_oad: "20050200",   item_07: "0204FF00",   start_pos:   0,        len_07:     12    }  #无功功率
  - {v_oad: "20050201",   item_07: "02040000",   start_pos:   0,        len_07:      3    }  #总无功功率
  - {v_oad: "20050202",   item_07: "02040100",   start_pos:   0,        len_07:      3    }  #A 相无功功率
  - {v_oad: "20050203",   item_07: "02040200",   start_pos:   0,        len_07:      3    }  #B 相无功功率
  - {v_oad: "20050204",   item_07: "02040300",   start_pos:   0,        len_07:      3    }  #C 相无功功率
  - {v_oad: "20060200",   item_07: "0205FF00",   start_pos:   0,        len_07:     12    }  #视在功率
  - {v_oad: "20060201",   item_07: "02050000",   start_pos:   0,        len_07:      3    }  #总视在功率
  - {v_oad: "20060202",   item_07: "02050100",   start_pos:   0,        len_07:      3    }  #A 相视在功率
  - {v_oad: "20060203",   item_07: "02050200",   start_pos:   0,        len_07:      3    }  #B 相视在功率
  - {v_oad: "20060204",   item_07: "02050300",   start_pos:   0,        len_07:      3    }  #C 相视在功率
  - {v_oad: "20070200",   item_07: "02800003",   start_pos:   0,        len_07:      3    }  #一分钟有功总平均功率
  - {v_oad: "20070201",   item_07: "02800003",   start_pos:   0,        len_07:      3    }  #一分钟有功总平均功率
  - {v_oad: "200A0200",   item_07: "0206FF00",   start_pos:   0,        len_07:      8    }  #功率因数
  - {v_oad: "200A0201",   item_07: "02060000",   start_pos:   0,        len_07:      2    }  #总功率因数
  - {v_oad: "200A0202",   item_07: "02060100",   start_pos:   0,        len_07:      2    }  #A相功率因数
  - {v_oad: "200A0203",   item_07: "02060200",   start_pos:   0,        len_07:      2    }  #B相功率因数
  - {v_oad: "200A0204",   item_07: "02060300",   start_pos:   0,        len_07:      2    }  #C相功率因数
  - {v_oad: "200B0200",   item_07: "0208FF00",   start_pos:   0,        len_07:      6    }  #电压波形失真度
  - {v_oad: "200B0201",   item_07: "02080100",   start_pos:   0,        len_07:      2    }  #A相电压波形失真度
  - {v_oad: "200B0202",   item_07: "02080200",   start_pos:   0,        len_07:      2    }  #B相电压波形失真度
  - {v_oad: "200B0203",   item_07: "02080300",   start_pos:   0,        len_07:      2    }  #C相电压波形失真度
  - {v_oad: "200C0200",   item_07: "0209FF00",   start_pos:   0,        len_07:      6    }  #电流波形失真度
  - {v_oad: "200C0201",   item_07: "02090100",   start_pos:   0,        len_07:      2    }  #A相电流波形失真度
  - {v_oad: "200C0202",   item_07: "02090200",   start_pos:   0,        len_07:      2    }  #B相电流波形失真度
  - {v_oad: "200C0203",   item_07: "02090300",   start_pos:   0,        len_07:      2    }  #C相电流波形失真度
  - {v_oad: "200D0200",   item_07: "020A01FF",   start_pos:   0,        len_07:     42    }  #A相电压谐波含有量（总及2…n次）
  - {v_oad: "200D0201",   item_07: "020A0101",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0202",   item_07: "020A0102",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0203",   item_07: "020A0103",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0204",   item_07: "020A0104",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0205",   item_07: "020A0105",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0206",   item_07: "020A0106",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0207",   item_07: "020A0107",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0208",   item_07: "020A0108",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0209",   item_07: "020A0109",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D020A",   item_07: "020A010A",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D020B",   item_07: "020A010B",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D020C",   item_07: "020A010C",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D020D",   item_07: "020A010D",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D020E",   item_07: "020A010E",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D020F",   item_07: "020A010F",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0210",   item_07: "020A0110",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0211",   item_07: "020A0111",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0212",   item_07: "020A0112",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0213",   item_07: "020A0113",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0214",   item_07: "020A0114",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0215",   item_07: "020A0115",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0300",   item_07: "020A02FF",   start_pos:   0,        len_07:     42    }  #B相电压谐波含有量（总及2…n次）
  - {v_oad: "200D0301",   item_07: "020A0201",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0302",   item_07: "020A0202",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0303",   item_07: "020A0203",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0304",   item_07: "020A0204",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0305",   item_07: "020A0205",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0306",   item_07: "020A0206",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0307",   item_07: "020A0207",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0308",   item_07: "020A0208",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0309",   item_07: "020A0209",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D030A",   item_07: "020A020A",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D030B",   item_07: "020A020B",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D030C",   item_07: "020A020C",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D030D",   item_07: "020A020D",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D030E",   item_07: "020A020E",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D030F",   item_07: "020A020F",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0310",   item_07: "020A0210",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0311",   item_07: "020A0211",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0312",   item_07: "020A0212",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0313",   item_07: "020A0213",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0314",   item_07: "020A0214",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0315",   item_07: "020A0215",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0400",   item_07: "020A03FF",   start_pos:   0,        len_07:     42    }  #C相电压谐波含有量（总及2…n次）
  - {v_oad: "200D0401",   item_07: "020A0301",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0402",   item_07: "020A0302",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0403",   item_07: "020A0303",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0404",   item_07: "020A0304",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0405",   item_07: "020A0305",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0406",   item_07: "020A0306",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0407",   item_07: "020A0307",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0408",   item_07: "020A0308",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0409",   item_07: "020A0309",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D040A",   item_07: "020A030A",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D040B",   item_07: "020A030B",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D040C",   item_07: "020A030C",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D040D",   item_07: "020A030D",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D040E",   item_07: "020A030E",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D040F",   item_07: "020A030F",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0410",   item_07: "020A0310",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0411",   item_07: "020A0311",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0412",   item_07: "020A0312",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0413",   item_07: "020A0313",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0414",   item_07: "020A0314",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200D0415",   item_07: "020A0315",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0200",   item_07: "020B01FF",   start_pos:   0,        len_07:     42    }  #A相电流谐波含有量（总及2…n次）
  - {v_oad: "200E0201",   item_07: "020B0101",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0202",   item_07: "020B0102",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0203",   item_07: "020B0103",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0204",   item_07: "020B0104",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0205",   item_07: "020B0105",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0206",   item_07: "020B0106",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0207",   item_07: "020B0107",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0208",   item_07: "020B0108",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0209",   item_07: "020B0109",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E020A",   item_07: "020B010A",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E020B",   item_07: "020B010B",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E020C",   item_07: "020B010C",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E020D",   item_07: "020B010D",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E020E",   item_07: "020B010E",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E020F",   item_07: "020B010F",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0210",   item_07: "020B0110",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0211",   item_07: "020B0111",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0212",   item_07: "020B0112",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0213",   item_07: "020B0113",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0214",   item_07: "020B0114",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0215",   item_07: "020B0115",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0300",   item_07: "020B02FF",   start_pos:   0,        len_07:     42    }  #B相电流谐波含有量（总及2…n次）
  - {v_oad: "200E0301",   item_07: "020B0201",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0302",   item_07: "020B0202",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0303",   item_07: "020B0203",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0304",   item_07: "020B0204",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0305",   item_07: "020B0205",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0306",   item_07: "020B0206",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0307",   item_07: "020B0207",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0308",   item_07: "020B0208",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0309",   item_07: "020B0209",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E030A",   item_07: "020B020A",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E030B",   item_07: "020B020B",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E030C",   item_07: "020B020C",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E030D",   item_07: "020B020D",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E030E",   item_07: "020B020E",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E030F",   item_07: "020B020F",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0310",   item_07: "020B0210",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0311",   item_07: "020B0211",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0312",   item_07: "020B0212",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0313",   item_07: "020B0213",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0314",   item_07: "020B0214",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0315",   item_07: "020B0215",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0400",   item_07: "020B03FF",   start_pos:   0,        len_07:     42    }  #C相电流谐波含有量（总及2…n次）
  - {v_oad: "200E0401",   item_07: "020B0301",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0402",   item_07: "020B0302",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0403",   item_07: "020B0303",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0404",   item_07: "020B0304",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0405",   item_07: "020B0305",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0406",   item_07: "020B0306",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0407",   item_07: "020B0307",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0408",   item_07: "020B0308",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0409",   item_07: "020B0309",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E040A",   item_07: "020B030A",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E040B",   item_07: "020B030B",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E040C",   item_07: "020B030C",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E040D",   item_07: "020B030D",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E040E",   item_07: "020B030E",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E040F",   item_07: "020B030F",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0410",   item_07: "020B0310",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0411",   item_07: "020B0311",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0412",   item_07: "020B0312",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0413",   item_07: "020B0313",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0414",   item_07: "020B0314",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200E0415",   item_07: "020B0315",   start_pos:   0,        len_07:      2    }
  - {v_oad: "200F0200",   item_07: "02800002",   start_pos:   0,        len_07:      2    }  #电网频率
  - {v_oad: "20100200",   item_07: "02800007",   start_pos:   0,        len_07:      2    }  #表内温度
  - {v_oad: "20110200",   item_07: "02800008",   start_pos:   0,        len_07:      2    }  #时钟电池电压
  - {v_oad: "20120200",   item_07: "02800009",   start_pos:   0,        len_07:      2    }  #停电抄表电池电压
  - {v_oad: "20130200",   item_07: "0280000A",   start_pos:   0,        len_07:      4    }  #时钟电池工作时间
  - {v_oad: "20170200",   item_07: "02800004",   start_pos:   0,        len_07:      3    }  #当前有功需量
  - {v_oad: "20180200",   item_07: "02800005",   start_pos:   0,        len_07:      3    }  #当前无功需量
  - {v_oad: "20190200",   item_07: "02800006",   start_pos:   0,        len_07:      3    }  #当前视在需量
  - {v_oad: "201A0200",   item_07: "02800020",   start_pos:   0,        len_07:      4    }  #当前电价
  - {v_oad: "201B0200",   item_07: "02800021",   start_pos:   0,        len_07:      4    }  #当前费率电价
  - {v_oad: "201C0200",   item_07: "0280000B",   start_pos:   0,        len_07:      4    }  #当前阶梯电价
  - {v_oad: "20140200",   item_07: "040005FF",   start_pos:   0,        len_07:     16    }  #电能表运行状态字
  - {v_oad: "20140201",   item_07: "04000501",   start_pos:   0,        len_07:      2    }
  - {v_oad: "20140202",   item_07: "04000502",   start_pos:   0,        len_07:      2    }
  - {v_oad: "20140203",   item_07: "04000503",   start_pos:   0,        len_07:      2    }
  - {v_oad: "20140204",   item_07: "04000504",   start_pos:   0,        len_07:      2    }
  - {v_oad: "20140205",   item_07: "04000505",   start_pos:   0,        len_07:      2    }
  - {v_oad: "20140206",   item_07: "04000506",   start_pos:   0,        len_07:      2    }
  - {v_oad: "20140207",   item_07: "04000507",   start_pos:   0,        len_07:      2    }
  - {v_oad: "202C0201",   item_07: "00900200",   start_pos:   0,        len_07:      4    }  #（当前）剩余金额
  - {v_oad: "202D0200",   item_07: "00900201",   start_pos:   0,        len_07:      4    }  #（当前）透支金额
  - {v_oad: "20310200",   item_07: "000C0000",   start_pos:   0,        len_07:      4    }  #当前月度组合有功总累计用电量
  - {v_oad: "20320200",   item_07: "000D0000",   start_pos:   0,        len_07:      4    }  #当前年结算周期组合有功总累计用电量
  - {v_oad: "21310200",   item_07: "03100100",   start_pos:   0,        len_07:     27    }  #A相当月A相电压合格率
  - {v_oad: "21310202",   item_07: "03100100",   start_pos:   0,        len_07:     27    }  #A相当月A相电压合格率
  - {v_oad: "21320200",   item_07: "03100200",   start_pos:   0,        len_07:     27    }  #B相当月A相电压合格率
  - {v_oad: "21320202",   item_07: "03100200",   start_pos:   0,        len_07:     27    }  #B相当月A相电压合格率
  - {v_oad: "21330200",   item_07: "03100300",   start_pos:   0,        len_07:     27    }  #C相当月A相电压合格率
  - {v_oad: "21330202",   item_07: "03100300",   start_pos:   0,        len_07:     27    }  #C相当月A相电压合格率
  - {v_oad: "30000501",   item_07: "04090101",   start_pos:   0,        len_07:      2    }  #失压事件电压触发上限
  - {v_oad: "30000502",   item_07: "04090102",   start_pos:   0,        len_07:      2    }  #失压事件电压恢复下限
  - {v_oad: "30000503",   item_07: "04090103",   start_pos:   0,        len_07:      3    }  #失压事件电流触发下限
  - {v_oad: "30000504",   item_07: "04090104",   start_pos:   0,        len_07:      1    }  #失压事件判定延时时间
  - {v_oad: "30000701",   item_07: "10010001",   start_pos:   0,        len_07:      3    }  #A相失压次数
  - {v_oad: "30000801",   item_07: "10020001",   start_pos:   0,        len_07:      3    }  #B相失压次数
  - {v_oad: "30000901",   item_07: "10030001",   start_pos:   0,        len_07:      3    }  #C相失压次数
                                            
  - {v_oad: "30010501",   item_07: "04090201",   start_pos:   0,        len_07:      2    }  #欠压事件电压触发上限
  - {v_oad: "30010502",   item_07: "04090202",   start_pos:   0,        len_07:      1    }  #欠压事件判定延时时间
  - {v_oad: "30020501",   item_07: "04090301",   start_pos:   0,        len_07:      2    }  #过压事件电压触发下限
  - {v_oad: "30020502",   item_07: "04090302",   start_pos:   0,        len_07:      1    }  #过压事件判定延时时间
  - {v_oad: "30030501",   item_07: "04090401",   start_pos:   0,        len_07:      2    }  #断相事件电压触发上限
  - {v_oad: "30030502",   item_07: "04090402",   start_pos:   0,        len_07:      3    }  #断相事件电流触发上限
  - {v_oad: "30030503",   item_07: "04090403",   start_pos:   0,        len_07:      1    }  #断相事件判定延时时间
                                             
  - {v_oad: "30040501",   item_07: "04090701",   start_pos:   0,        len_07:      2    }  #失流事件电压触发下限
  - {v_oad: "30040502",   item_07: "04090702",   start_pos:   0,        len_07:      3    }  #失流事件电流触发上限
  - {v_oad: "30040503",   item_07: "04090703",   start_pos:   0,        len_07:      3    }  #失流事件电流触发下限
  - {v_oad: "30040504",   item_07: "04090704",   start_pos:   0,        len_07:      1    }  #失流事件判定延时时间
  - {v_oad: "30040701",   item_07: "18010001",   start_pos:   0,        len_07:      3    }  #A相失流次数
  - {v_oad: "30040801",   item_07: "18020001",   start_pos:   0,        len_07:      3    }  #B相失流次数
  - {v_oad: "30040901",   item_07: "18030001",   start_pos:   0,        len_07:      3    }  #C相失流次数
  - {v_oad: "30050501",   item_07: "04090801",   start_pos:   0,        len_07:      2    }  #过流事件电流触发下限
  - {v_oad: "30050502",   item_07: "04090802",   start_pos:   0,        len_07:      1    }  #过流事件判定延时时间
  - {v_oad: "30060501",   item_07: "04090901",   start_pos:   0,        len_07:      2    }  #断流事件电压触发下限
  - {v_oad: "30060502",   item_07: "04090902",   start_pos:   0,        len_07:      3    }  #断流事件电流触发上限
  - {v_oad: "30060503",   item_07: "04090903",   start_pos:   0,        len_07:      1    }  #断流事件判定延时时间
  - {v_oad: "30070501",   item_07: "04091001",   start_pos:   0,        len_07:      3    }  #功率反向事件有功功率触发下限
  - {v_oad: "30070502",   item_07: "04091002",   start_pos:   0,        len_07:      1    }  #功率反向事件判定延时时间
  - {v_oad: "30070701",   item_07: "1B010001",   start_pos:   0,        len_07:      3    }  #A相潮流反向次数
  - {v_oad: "30070801",   item_07: "1B020001",   start_pos:   0,        len_07:      3    }  #B相潮流反向次数
  - {v_oad: "30070901",   item_07: "1B030001",   start_pos:   0,        len_07:      3    }  #C相潮流反向次数
  - {v_oad: "30080501",   item_07: "04090B01",   start_pos:   0,        len_07:      3    }  #过载事件有功功率触发下限
  - {v_oad: "30080502",   item_07: "04090B02",   start_pos:   0,        len_07:      1    }  #过载事件判定延时时间
  - {v_oad: "30090601",   item_07: "04090D01",   start_pos:   0,        len_07:      3    }  #有功需量超限事件需量触发下限
  - {v_oad: "30090602",   item_07: "04090D03",   start_pos:   0,        len_07:      1    }  #需量超限事件判定延时时间
  - {v_oad: "300A0601",   item_07: "04090D01",   start_pos:   0,        len_07:      3    }  #有功需量超限事件需量触发下限
  - {v_oad: "300A0602",   item_07: "04090D03",   start_pos:   0,        len_07:      1    }  #需量超限事件判定延时时间
  - {v_oad: "300B0601",   item_07: "04090D02",   start_pos:   0,        len_07:      3    }  #有功需量超限事件需量触发下限
  - {v_oad: "300B0602",   item_07: "04090D03",   start_pos:   0,        len_07:      1    }  #需量超限事件判定延时时间
  - {v_oad: "300C0601",   item_07: "04090E01",   start_pos:   0,        len_07:      2    }  #功率因数超下限阀值
  - {v_oad: "300C0602",   item_07: "04090E02",   start_pos:   0,        len_07:      1    }  #功率因数超下限判定延时时间
  - {v_oad: "30120701",   item_07: "03300000",   start_pos:   0,        len_07:      3    }  #编程总次数
  - {v_oad: "30130701",   item_07: "03300100",   start_pos:   0,        len_07:      3    }  #清零总次数
  - {v_oad: "30140701",   item_07: "03300200",   start_pos:   0,        len_07:      3    }  #需量清零事件总次数
  - {v_oad: "30150701",   item_07: "03300300",   start_pos:   0,        len_07:      3    }  #事件清零总次数
  - {v_oad: "30160701",   item_07: "03300400",   start_pos:   0,        len_07:      3    }  #校时总次数
  - {v_oad: "30170701",   item_07: "03300500",   start_pos:   0,        len_07:      3    }  #电能表时段编程总次数
  - {v_oad: "301B0701",   item_07: "03300D00",   start_pos:   0,        len_07:      3    }  #开盖总次数
  - {v_oad: "301C0701",   item_07: "03300E00",   start_pos:   0,        len_07:      3    }  #开端钮盒总次数
  - {v_oad: "301D0601",   item_07: "04090501",   start_pos:   0,        len_07:      2    }  #电压不平衡率限值
  - {v_oad: "301D0602",   item_07: "04090502",   start_pos:   0,        len_07:      1    }  #电压不平衡率判定延时时间
  - {v_oad: "301F0701",   item_07: "1D000101",   start_pos:   0,        len_07:      3    }  #跳闸总次数
  - {v_oad: "301E0601",   item_07: "04090601",   start_pos:   0,        len_07:      2    }  #电流不平衡率限值
  - {v_oad: "301E0602",   item_07: "04090602",   start_pos:   0,        len_07:      1    }  #电流不平衡率判定延时时间
  - {v_oad: "30280701",   item_07: "00330201",   start_pos:   0,        len_07:      2    }  #电能表购电记录
  - {v_oad: "302D0601",   item_07: "04090F01",   start_pos:   0,        len_07:      2    }  #电流严重不平衡限值
  - {v_oad: "302D0602",   item_07: "04090F02",   start_pos:   0,        len_07:      1    }  #电流严重不平衡触发延时时间
  - {v_oad: "40010200",   item_07: "04000401",   start_pos:   0,        len_07:      6    }  #通信地址
  - {v_oad: "40020200",   item_07: "04000402",   start_pos:   0,        len_07:      6    }  #表号
  - {v_oad: "40030200",   item_07: "0400040E",   start_pos:   0,        len_07:      6    }  #客户编号
  - {v_oad: "40040200",   item_07: "0400040F",   start_pos:   0,        len_07:     11    }  #设备地理位置
  - {v_oad: "40040201",   item_07: "0400040F",   start_pos:   0,        len_07:     11    }
  - {v_oad: "40040202",   item_07: "0400040F",   start_pos:   0,        len_07:     11    }
  - {v_oad: "40040203",   item_07: "0400040F",   start_pos:   0,        len_07:     11    }
  - {v_oad: "40080200",   item_07: "04000106",   start_pos:   0,        len_07:      5    }  #备用套时区表切换时间
  - {v_oad: "40090200",   item_07: "04000107",   start_pos:   0,        len_07:      5    }  #备用套日时段切换时间
  - {v_oad: "400A0200",   item_07: "04000108",   start_pos:   0,        len_07:      5    }  #备用套分时费率切换时间
  - {v_oad: "400B0200",   item_07: "04000109",   start_pos:   0,        len_07:      5    }  #备用套阶梯电价切换时间
  - {v_oad: "400C0201",   item_07: "04000201",   start_pos:   0,        len_07:      1    }  #年时区数p≤14
  - {v_oad: "400C0202",   item_07: "04000202",   start_pos:   0,        len_07:      1    }  #日时段表数q≤8
  - {v_oad: "400C0203",   item_07: "04000203",   start_pos:   0,        len_07:      1    }  #日时段数(每日切换数)m≤14
  - {v_oad: "400C0204",   item_07: "04000204",   start_pos:   0,        len_07:      1    }  #费率数k≤63
  - {v_oad: "400C0205",   item_07: "04000205",   start_pos:   0,        len_07:      2    }  #费率数k≤63
  - {v_oad: "400D0200",   item_07: "04000207",   start_pos:   0,        len_07:      1    }  #阶梯数
  - {v_oad: "400E0200",   item_07: "04000206",   start_pos:   0,        len_07:      1    }  #谐波分析次数
  - {v_oad: "400F0200",   item_07: "04000208",   start_pos:   0,        len_07:      1    }  #密钥总条数
  - {v_oad: "40120200",   item_07: "04000801",   start_pos:   0,        len_07:      1    }  #周休日特征字
  - {v_oad: "40130200",   item_07: "04000802",   start_pos:   0,        len_07:      1    }  #周休日用的日时段表号
  - {v_oad: "40140200",   item_07: "04010000",   start_pos:   0,        len_07:     42    }  #当前套时区表
  - {v_oad: "40160200",   item_07: "04010001",   start_pos:   0,        len_07:     42    }  #当前套日时段表
  - {v_oad: "401C0200",   item_07: "04000306",   start_pos:   0,        len_07:      3    }  #电流互感器变比
  - {v_oad: "401D0200",   item_07: "04000307",   start_pos:   0,        len_07:      3    }  #电压互感器变比
  - {v_oad: "401E0201",   item_07: "04001001",   start_pos:   0,        len_07:      4    }  #报警金额限值1
  - {v_oad: "401E0202",   item_07: "04001002",   start_pos:   0,        len_07:      4    }  #报警金额限值2
  - {v_oad: "401F0201",   item_07: "04001003",   start_pos:   0,        len_07:      4    }  #透支金额限值
  - {v_oad: "401F0202",   item_07: "04001004",   start_pos:   0,        len_07:      4    }  #囤积金额限值
  - {v_oad: "401F0203",   item_07: "04001005",   start_pos:   0,        len_07:      4    }  #合闸允许金额限值
  - {v_oad: "40200201",   item_07: "04000F01",   start_pos:   0,        len_07:      4    }  #报警电量限值1
  - {v_oad: "40200202",   item_07: "04000F02",   start_pos:   0,        len_07:      4    }  #报警电量限值2
  - {v_oad: "40210201",   item_07: "04000F03",   start_pos:   0,        len_07:      4    }  #囤积电量限值
  - {v_oad: "40210202",   item_07: "04000F04",   start_pos:   0,        len_07:      4    }  #透支电量限值
  - {v_oad: "40220200",   item_07: "04001502",   start_pos:   0,        len_07:      2    }  #插卡状态字
  - {v_oad: "40300201",   item_07: "04090C01",   start_pos:   0,        len_07:      2    }  #电压考核上限
  - {v_oad: "40300202",   item_07: "04090C02",   start_pos:   0,        len_07:      2    }  #电压考核下限
  - {v_oad: "40300203",   item_07: "04000E03",   start_pos:   0,        len_07:      2    }  #电压合格上限
  - {v_oad: "40300204",   item_07: "04000E04",   start_pos:   0,        len_07:      2    }  #电压合格下限
  - {v_oad: "41000200",   item_07: "04000103",   start_pos:   0,        len_07:      1    }  #最大需量周期
  - {v_oad: "41010200",   item_07: "04000104",   start_pos:   0,        len_07:      1    }  #滑差时间
  - {v_oad: "41020200",   item_07: "04000105",   start_pos:   0,        len_07:      2    }  #校表脉冲宽度
  - {v_oad: "41030200",   item_07: "04000403",   start_pos:   0,        len_07:     32    }  #资产管理编码
  - {v_oad: "41040200",   item_07: "04000404",   start_pos:   0,        len_07:      6    }  #额定电压
  - {v_oad: "41050200",   item_07: "04000405",   start_pos:   0,        len_07:      6    }  #额定电流/基本电流
  - {v_oad: "41060200",   item_07: "04000406",   start_pos:   0,        len_07:      6    }  #最大电流
  - {v_oad: "41070200",   item_07: "04000407",   start_pos:   0,        len_07:      4    }  #有功准确度等级
  - {v_oad: "41080200",   item_07: "04000408",   start_pos:   0,        len_07:      4    }  #无功准确度等级
  - {v_oad: "41090200",   item_07: "04000409",   start_pos:   0,        len_07:      3    }  #电能表有功常数
  - {v_oad: "410A0200",   item_07: "0400040A",   start_pos:   0,        len_07:      3    }  #电能表无功常数
  - {v_oad: "410B0200",   item_07: "0400040B",   start_pos:   0,        len_07:     10    }  #电能表型号
  - {v_oad: "410C0201",   item_07: "04000D01",   start_pos:   0,        len_07:      2    }  #A 相电导
  - {v_oad: "410C0202",   item_07: "04000D05",   start_pos:   0,        len_07:      2    }  #B 相电导
  - {v_oad: "410C0203",   item_07: "04000D09",   start_pos:   0,        len_07:      2    }  #C 相电导
  - {v_oad: "410D0201",   item_07: "04000D04",   start_pos:   0,        len_07:      2    }  #A 相电抗
  - {v_oad: "410D0202",   item_07: "04000D08",   start_pos:   0,        len_07:      2    }  #B 相电抗
  - {v_oad: "410D0203",   item_07: "04000D0C",   start_pos:   0,        len_07:      2    }  #C 相电抗
  - {v_oad: "410E0201",   item_07: "04000D03",   start_pos:   0,        len_07:      2    }  #A 相电阻
  - {v_oad: "410E0202",   item_07: "04000D07",   start_pos:   0,        len_07:      2    }  #B 相电阻
  - {v_oad: "410E0203",   item_07: "04000D0B",   start_pos:   0,        len_07:      2    }  #C 相电阻
  - {v_oad: "410F0201",   item_07: "04000D02",   start_pos:   0,        len_07:      2    }  #A 相电纳
  - {v_oad: "410F0202",   item_07: "04000D06",   start_pos:   0,        len_07:      2    }  #B 相电纳
  - {v_oad: "410F0203",   item_07: "04000D0A",   start_pos:   0,        len_07:      2    }  #C 相电纳
  - {v_oad: "41110200",   item_07: "04800004",   start_pos:   0,        len_07:      8    }  #软件备案号
  - {v_oad: "41120200",   item_07: "04000601",   start_pos:   0,        len_07:      1    }  #有功组合方式特征字
  - {v_oad: "41130200",   item_07: "04000602",   start_pos:   0,        len_07:      1    }  #无功组合方式1特征字
  - {v_oad: "41140200",   item_07: "04000603",   start_pos:   0,        len_07:      1    }  #无功组合方式2特征字
  - {v_oad: "41160201",   item_07: "04000B01",   start_pos:   0,        len_07:      2    }  #每月第1结算日
  - {v_oad: "41160202",   item_07: "04000B02",   start_pos:   0,        len_07:      2    }  #每月第2结算日
  - {v_oad: "41160203",   item_07: "04000B03",   start_pos:   0,        len_07:      2    }  #每月第3结算日
  - {v_oad: "43000200",   item_07: "0400040B",   start_pos:   0,        len_07:     10    }  #电表型号（ASCII码）
  - {v_oad: "43000301",   item_07: "04800003",   start_pos:   0,        len_07:     32    }  #厂家编号(ASCII码)
  - {v_oad: "43000302",   item_07: "04800001",   start_pos:   0,        len_07:     32    }  #厂家软件版本号(ASCII码)
  - {v_oad: "43000304",   item_07: "04800002",   start_pos:   0,        len_07:     32    }  #厂家硬件版本号(ASCII码)
  - {v_oad: "43000400",   item_07: "0400040C",   start_pos:   0,        len_07:     10    }  #生产日期（ASCII码）
  - {v_oad: "44000301",   item_07: "0400040C",   start_pos:   0,        len_07:     16    }  #协议版本号（ASCII码）
  - {v_oad: "45000900",   item_07: "04001301",   start_pos:   0,        len_07:      1    }  #无线通信在线及信号强弱指示
  - {v_oad: "80000201",   item_07: "04001402",   start_pos:   0,        len_07:      3    }  #继电器拉闸控制电流门限值
  - {v_oad: "80000202",   item_07: "04001403",   start_pos:   0,        len_07:      3    }  #超拉闸控制电流门限保护延时时间
  - {v_oad: "F1000600",   item_07: "02800022",   start_pos:   0,        len_07:      2    }  #身份认证时效剩余时间
  - {v_oad: "F1000400",   item_07: "04000508",   start_pos:   0,        len_07:      4    }  #密钥状态字
  - {v_oad: "F1000E00",   item_07: "04001404",   start_pos:   0,        len_07:      1    }  #红外认证时效
  - {v_oad: "F1000F00",   item_07: "02800023",   start_pos:   0,        len_07:      1    }  #红外认证时效剩余时间
  - {v_oad: "F2020201",   item_07: "04000701",   start_pos:   0,        len_07:      1    }  #调制型红外光口波特率特征字
  - {v_oad: "F2020202",   item_07: "04000702",   start_pos:   0,        len_07:      1    }  #接触式红外光口波特率特征字
  - {v_oad: "F2010201",   item_07: "04000703",   start_pos:   0,        len_07:      1    }  #通信口1波特率特征字
  - {v_oad: "F2010202",   item_07: "04000704",   start_pos:   0,        len_07:      1    }  #通信口2波特率特征字
  - {v_oad: "F2090201",   item_07: "04000705",   start_pos:   0,        len_07:      1    }  #通信口3波特率特征字
  - {v_oad: "F3000500",   item_07: "04040300",   start_pos:   0,        len_07:      5    }  #液晶查看(循显)
  - {v_oad: "F3010500",   item_07: "04040300",   start_pos:   0,        len_07:      5    }  #液晶查看(按显)
  - {v_oad: "F3010500",   item_07: "04040300",   start_pos:   0,        len_07:      5    }  #液晶查看(按显)
