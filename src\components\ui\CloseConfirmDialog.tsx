import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectCloseToTray, setCloseToTray } from '../../store/slices/settingsSlice';

interface CloseConfirmDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onMinimizeToTray: () => void;
    onExitApp: () => void;
}

export const CloseConfirmDialog: React.FC<CloseConfirmDialogProps> = ({
    isOpen,
    onClose,
    onMinimizeToTray,
    onExitApp
}) => {
    const dispatch = useDispatch();
    const closeToTray = useSelector(selectCloseToTray);
    const [rememberChoice, setRememberChoice] = useState(false);

    const handleMinimizeToTray = () => {
        if (rememberChoice) {
            dispatch(setCloseToTray(true));
        }
        onMinimizeToTray();
        onClose();
    };

    const handleExitApp = () => {
        if (rememberChoice) {
            dispatch(setCloseToTray(false));
        }
        onExitApp();
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* 背景遮罩 */}
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose}></div>
            
            {/* 对话框 */}
            <div className="relative bg-base-100 rounded-lg shadow-xl p-6 w-96 max-w-md mx-4">
                <div className="flex flex-col space-y-4">
                    {/* 标题 */}
                    <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                            <svg className="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-base-content">
                            关闭应用程序
                        </h3>
                    </div>

                    {/* 内容 */}
                    <div className="text-base-content/80">
                        <p className="mb-4">
                            您希望如何关闭应用程序？
                        </p>
                        <div className="space-y-2 text-sm">
                            <div className="flex items-center space-x-2">
                                <div className="w-2 h-2 bg-info rounded-full"></div>
                                <span><strong>最小化到托盘</strong> - 应用程序将在后台继续运行</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="w-2 h-2 bg-error rounded-full"></div>
                                <span><strong>完全退出</strong> - 应用程序将完全关闭</span>
                            </div>
                        </div>
                    </div>

                    {/* 记住选择 */}
                    <div className="form-control">
                        <label className="label cursor-pointer justify-start space-x-3">
                            <input
                                type="checkbox"
                                className="checkbox checkbox-sm"
                                checked={rememberChoice}
                                onChange={(e) => setRememberChoice(e.target.checked)}
                            />
                            <span className="label-text">记住我的选择，下次不再询问</span>
                        </label>
                    </div>

                    {/* 按钮组 */}
                    <div className="flex flex-col space-y-2 pt-2">
                        <button
                            className="btn btn-info btn-sm"
                            onClick={handleMinimizeToTray}
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                            最小化到托盘
                        </button>
                        
                        <button
                            className="btn btn-error btn-sm"
                            onClick={handleExitApp}
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            完全退出
                        </button>
                        
                        <button
                            className="btn btn-ghost btn-sm"
                            onClick={onClose}
                        >
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};
