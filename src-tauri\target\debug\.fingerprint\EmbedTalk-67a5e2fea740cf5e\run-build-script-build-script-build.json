{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9578864029216354803, "build_script_build", false, 15039494317302642136], [10755362358622467486, "build_script_build", false, 7946544574408380096], [371826037427084582, "build_script_build", false, 6596353702036617476], [5157003953992891593, "build_script_build", false, 11716531897010386832], [13592916204794590741, "build_script_build", false, 13163602749149909167], [10436516057248261217, "build_script_build", false, 9565878238615177638], [7236291379133587555, "build_script_build", false, 5873082804914703439], [12676100885892732016, "build_script_build", false, 9702580663948179290], [17509843537913359226, "build_script_build", false, 14297722310597972777], [1582828171158827377, "build_script_build", false, 17504906040651575257], [4368795134222496747, "build_script_build", false, 2998581239578878135], [11721252211900136025, "build_script_build", false, 3621123297434766418], [16445525635439251275, "build_script_build", false, 10652918927488717861]], "local": [{"RerunIfChanged": {"output": "debug\\build\\EmbedTalk-67a5e2fea740cf5e\\output", "paths": ["tauri.conf.json", "capabilities", "resources\\protocolconfig\\CSG13.xml", "resources\\protocolconfig\\CSG16.xml", "resources\\protocolconfig\\DLT645.xml", "resources\\protocolconfig\\MOUDLE.xml", "resources\\taskoadconfig\\50020200_list.yml", "resources\\taskoadconfig\\50040200_list.yml", "resources\\taskoadconfig\\50060200_list.yml", "resources\\taskoadconfig\\oad_list.yml"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}