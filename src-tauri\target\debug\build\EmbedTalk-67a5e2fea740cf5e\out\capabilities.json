{"default": {"identifier": "default", "description": "Capability for the main window", "local": true, "windows": ["main", "quickparse", "frame-parse", "frame-parse-window"], "permissions": ["fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-copy-file", "fs:allow-mkdir", "fs:allow-remove", "fs:allow-rename", "fs:allow-exists", {"identifier": "fs:default", "allow": [{"path": "$APPDATA/*"}, {"path": "$HOME/*"}, {"path": "$EXE/*"}]}, {"identifier": "fs:allow-resource-read", "allow": [{"path": "$RESOURCE/*"}]}, {"identifier": "fs:scope", "allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/**"}, {"path": "$RESOURCE/**"}, {"path": "$RESOURCE"}]}, "core:window:default", "core:window:allow-create", "core:window:allow-center", "core:window:allow-request-user-attention", "core:window:allow-set-resizable", "core:window:allow-set-maximizable", "core:window:allow-set-minimizable", "core:window:allow-set-closable", "core:window:allow-set-title", "core:window:allow-maximize", "core:window:allow-toggle-maximize", "core:window:allow-unmaximize", "core:window:allow-minimize", "core:window:allow-unminimize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-close", "core:window:allow-set-decorations", "core:window:allow-set-always-on-top", "core:window:allow-set-content-protected", "core:window:allow-set-size", "core:window:allow-set-min-size", "core:window:allow-set-max-size", "core:window:allow-set-position", "core:window:allow-set-fullscreen", "core:window:allow-set-focus", "core:window:allow-set-icon", "core:window:allow-set-skip-taskbar", "core:window:allow-set-cursor-grab", "core:window:allow-set-cursor-visible", "core:window:allow-set-cursor-icon", "core:window:allow-set-cursor-position", "core:window:allow-set-ignore-cursor-events", "core:window:allow-start-dragging", "core:window:allow-is-visible", "core:window:allow-get-all-windows", "core:menu:allow-new", "core:webview:allow-print", "core:webview:allow-internal-toggle-devtools", "core:event:allow-emit", "core:event:allow-unlisten", "core:event:allow-listen", "core:event:allow-emit-to", "core:window:allow-theme", "core:window:allow-set-theme", "core:window:allow-current-monitor", "core:window:allow-primary-monitor", "core:window:allow-inner-position", "core:window:allow-inner-size", "core:window:deny-is-resizable", "core:window:allow-is-minimized", "core:window:allow-is-maximized", "core:webview:allow-create-webview-window", "core:webview:allow-get-all-webviews", "core:webview:allow-set-webview-focus", "core:webview:default", "core:app:allow-set-app-theme", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-register-all", "global-shortcut:allow-unregister", "global-shortcut:allow-unregister-all", "shell:allow-open", "dialog:allow-open", "dialog:allow-save", "dialog:allow-message", "dialog:allow-ask", "dialog:allow-confirm", "os:allow-platform", "os:allow-version", "os:allow-os-type", "os:allow-family", "os:allow-arch", "os:allow-exe-extension", "os:allow-locale", "os:allow-hostname", "clipboard-manager:default", "clipboard-manager:allow-read-text", "clipboard-manager:allow-write-text", "process:default", "process:allow-exit", "process:allow-restart", "core:path:allow-dirname", "core:path:allow-resolve-directory", "core:path:default", "sql:default", "sql:allow-close", "sql:allow-execute", "sql:allow-load", "sql:allow-select", "log:allow-log", "log:default", "core:app:allow-app-hide", "core:app:allow-default-window-icon", "core:app:allow-app-show", "core:app:allow-version", "core:app:default", "core:tray:allow-new", "core:tray:allow-set-icon", "core:tray:allow-set-menu", "core:tray:allow-set-show-menu-on-left-click", "core:tray:allow-set-title", "core:tray:allow-set-tooltip", "core:tray:allow-set-visible", "core:tray:default", "updater:default", "shell:default", "shell:allow-execute", "shell:allow-kill", "shell:allow-stdin-write", {"identifier": "shell:allow-execute", "allow": [{"args": [{"validator": ".*"}], "cmd": "explorer", "name": "explorer-dir"}, {"args": [{"validator": ".*"}], "cmd": "open", "name": "open-dir-mac"}, {"args": [{"validator": ".*"}], "cmd": "xdg-open", "name": "open-dir-linux"}]}]}, "desktop-capability": {"identifier": "desktop-capability", "description": "", "local": true, "permissions": ["window-state:default", "global-shortcut:default", "updater:default"], "platforms": ["macOS", "windows", "linux"]}}