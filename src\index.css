@tailwind base;
@tailwind components;
@tailwind utilities;

/* react-resizable 样式 */
.react-resizable {
    position: relative;
    background-clip: padding-box;
    overflow: visible;
}

.react-resizable-handle {
    position: absolute;
    width: 10px;
    height: 100%;
    top: 0;
    right: -5px;
    cursor: col-resize;
    z-index: 10;
    background-color: transparent;
}

/* 隐藏滚动条 */
.hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.react-resizable-handle::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 1px;
    background-color: rgba(0, 0, 0, 0.2);
    transform: translateX(-50%);
    transition: background-color 0.2s;
}

.react-resizable-handle:hover::after {
    background-color: rgba(0, 0, 0, 0.5);
} 