Vir_50040200_List: &Vir_50040200_List
  - { v_oad: "20210200", item_07: "05060001", start_pos: 0, len_07: 5 } #日冻结时间
  - { v_oad: "00100200", item_07: "05060101", start_pos: 0, len_07: 20 } #日冻结正向有功电能数据
  - { v_oad: "00200200", item_07: "05060201", start_pos: 0, len_07: 20 } #日冻结反向有功电能数据
  - { v_oad: "00300200", item_07: "05060301", start_pos: 0, len_07: 20 } #日冻结组合无功1电能数据
  - { v_oad: "00400200", item_07: "05060401", start_pos: 0, len_07: 20 } #日冻结组合无功2电能数据
  - { v_oad: "00500200", item_07: "05060501", start_pos: 0, len_07: 20 } #日冻结第一象限无功电能数据
  - { v_oad: "00600200", item_07: "05060601", start_pos: 0, len_07: 20 } #日冻结第二象限无功电能数据
  - { v_oad: "00700200", item_07: "05060701", start_pos: 0, len_07: 20 } #日冻结第三象限无功电能数据
  - { v_oad: "00800200", item_07: "05060801", start_pos: 0, len_07: 20 } #日冻结第四象限无功电能数据
  - { v_oad: "10100200", item_07: "05060901", start_pos: 0, len_07: 20 } #日冻结正向有功最大需量及发生时间数据
  - { v_oad: "10200200", item_07: "05060A01", start_pos: 0, len_07: 20 } #日冻结反向有功最大需量及发生时间数据
  - { v_oad: "10300200", item_07: "0103FF00", start_pos: 0, len_07: 40 } #组合无功1最大需量
  - { v_oad: "10300201", item_07: "01030000", start_pos: 0, len_07: 8 } #组合无功1最大需量及发生时间
  - { v_oad: "10300202", item_07: "01030100", start_pos: 0, len_07: 8 } #组合无功1费率1 最大需量及发生时间
  - { v_oad: "10300203", item_07: "01030200", start_pos: 0, len_07: 8 } #组合无功1费率2 最大需量及发生时间
  - { v_oad: "10300204", item_07: "01030300", start_pos: 0, len_07: 8 } #组合无功1费率3 最大需量及发生时间
  - { v_oad: "10300205", item_07: "01030400", start_pos: 0, len_07: 8 } #组合无功1费率4 最大需量及发生时间
  - { v_oad: "10400200", item_07: "0104FF00", start_pos: 0, len_07: 40 } #组合无功2最大需量
  - { v_oad: "10400201", item_07: "01040000", start_pos: 0, len_07: 8 } #组合无功2最大需量及发生时间
  - { v_oad: "10400202", item_07: "01040100", start_pos: 0, len_07: 8 } #组合无功2费率1 最大需量及发生时间
  - { v_oad: "10400203", item_07: "01040200", start_pos: 0, len_07: 8 } #组合无功2费率2 最大需量及发生时间
  - { v_oad: "10400204", item_07: "01040300", start_pos: 0, len_07: 8 } #组合无功2费率3 最大需量及发生时间
  - { v_oad: "10400205", item_07: "01040400", start_pos: 0, len_07: 8 } #组合无功2费率4 最大需量及发生时间
  - { v_oad: "20040200", item_07: "05061001", start_pos: 0, len_07: 12 } #有功功率
  - { v_oad: "20050200", item_07: "05061001", start_pos: 12, len_07: 12 } #无功功率
  - { v_oad: "202C0200", item_07: "05080201", start_pos: 0, len_07: 10 } #钱包文件
  - { v_oad: "202C0202", item_07: "05080201", start_pos: 0, len_07: 2 } #购电次数
  - { v_oad: "202C0201", item_07: "05080201", start_pos: 2, len_07: 4 } #剩余金额
  - { v_oad: "202D0200", item_07: "05080201", start_pos: 6, len_07: 4 } #透支金额
  - { v_oad: "20140200", item_07: "040005FF", start_pos: 0, len_07: 18 } #电表运行状态字数据块
  - { v_oad: "301B0701", item_07: "03300D00", start_pos: 0, len_07: 3 } #开表盖总次数
  - { v_oad: "84000504", item_07: "04000504", start_pos: 0, len_07: 3 }
  - { v_oad: "84000505", item_07: "04000505", start_pos: 2, len_07: 2 } #A相状态字本月
  - { v_oad: "84000506", item_07: "04000506", start_pos: 2, len_07: 2 } #B相状态字本月
  - { v_oad: "90000001", item_07: "10000001", start_pos: 2, len_07: 2 } #C相状态字本月
  - { v_oad: "90000101", item_07: "10000101", start_pos: 3, len_07: 3 } #失压总次数本月
  - { v_oad: "90000201", item_07: "10000201", start_pos: 6, len_07: 6 } #(最近1次)失压发生时
  - { v_oad: "93000001", item_07: "13000001", start_pos: 6, len_07: 6 } #(最近1次)失压结束时
  - { v_oad: "93000101", item_07: "13000101", start_pos: 3, len_07: 3 } #断相总次数本月
  - { v_oad: "93000201", item_07: "13000201", start_pos: 6, len_07: 6 } #(最近1次)断相发生时
  - { v_oad: "83300D00", item_07: "03300D00", start_pos: 6, len_07: 6 } #(最近1次)断相结束时
  - { v_oad: "83300D01", item_07: "03300D01", start_pos: 3, len_07: 3 } #开表盖总次数本月
  - { v_oad: "98000001", item_07: "18000001", start_pos: 60, len_07: 60 } #(上1 次)开表盖记
  - { v_oad: "98000101", item_07: "18000101", start_pos: 3, len_07: 3 } #失流总次数本月
  - { v_oad: "98000201", item_07: "18000201", start_pos: 6, len_07: 6 } #(最近1次)失流发生时
  - { v_oad: "83350000", item_07: "03350000", start_pos: 6, len_07: 6 } #(最近1次)失流结束时
  - { v_oad: "83350001", item_07: "03350001", start_pos: 3, len_07: 3 } #恒定磁场干扰总次数
  - { v_oad: "95000001", item_07: "15000001", start_pos: 28, len_07: 28 } #(上1 次)恒定磁场干
  - { v_oad: "95000101", item_07: "15000101", start_pos: 3, len_07: 6 } #电流逆相序总次数
  - { v_oad: "95001201", item_07: "15001201", start_pos: 6, len_07: 6 } #(上1 次)电流逆相序
  - { v_oad: "94000001", item_07: "14000001", start_pos: 6, len_07: 6 } #(上1 次)电流逆相序
  - { v_oad: "94000101", item_07: "14000101", start_pos: 6, len_07: 6 } #电压逆相序总次数
  - { v_oad: "94001201", item_07: "14001201", start_pos: 6, len_07: 6 } #(上1 次)电压逆相序
  - { v_oad: "80150000", item_07: "00150000", start_pos: 6, len_07: 6 } #(上1 次)电压逆相序
  - { v_oad: "80290000", item_07: "00290000", start_pos: 4, len_07: 4 } #当前 A相正向有功电能
  - { v_oad: "803D0000", item_07: "003D0000", start_pos: 4, len_07: 4 } #当前 B相正向有功电能
  - { v_oad: "83700001", item_07: "03700001", start_pos: 4, len_07: 4 } #当前 C相正向有功电能
  - { v_oad: "83700101", item_07: "03700101", start_pos: 29, len_07: 29 } #（本日）总电压合格率统
  - { v_oad: "83700201", item_07: "03700201", start_pos: 29, len_07: 29 } #（本日）A电压合格率统
  - { v_oad: "83700301", item_07: "03700301", start_pos: 29, len_07: 29 } #（本日）B电压合格率统
  - { v_oad: "83700601", item_07: "03700601", start_pos: 29, len_07: 29 } #（本日）C电压合格率统
  - { v_oad: "83700701", item_07: "03700701", start_pos: 38, len_07: 38 } #电表日功率负载统计
  - { v_oad: "840015FF", item_07: "04001501", start_pos: 4, len_07: 4 } #谐波畸变率统计值
  - { v_oad: "85E00001", item_07: "05E00001", start_pos: 12, len_07: 12 } #日主动上报状态字
  - { v_oad: "83E00101", item_07: "03E00101", start_pos: 42, len_07: 42 } #用户负荷识别数据
  - { v_oad: "85E00201", item_07: "05E00201", start_pos: 33, len_07: 33 } #电表日功率误差统计数据
  - { v_oad: "85E00101", item_07: "05E00101", start_pos: 145, len_07: 145 } #电表日电压超限统计数据
  - { v_oad: "84000101", item_07: "04000101", start_pos: 7, len_07: 7 } #电表日运行信息本月
  - { v_oad: "84000102", item_07: "04000102", start_pos: 4, len_07: 4 } # 电能表日期及星期
  - { v_oad: "83E00201", item_07: "03E00201", start_pos: 0, len_07: 91 } # 电表用电安全日数据
  - { v_oad: "83E00401", item_07: "03E00401", start_pos: 0, len_07: 19 } # 电表日电量统计数据
