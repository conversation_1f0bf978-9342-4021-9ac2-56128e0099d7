name: "publish"

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+*'

jobs:
  create-release:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    outputs:
      release_id: ${{ steps.create-release.outputs.result }}
      changelog: ${{ steps.changelog.outputs.changelog }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: get version from tag
        run: echo "PACKAGE_VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_ENV

      - name: Generate Changelog
        id: changelog
        run: |
          # 获取当前tag和上一个tag
          current_tag=${GITHUB_REF#refs/tags/}
          previous_tag=$(git tag --sort=-v:refname | grep "^v" | grep -A1 "^${current_tag}$" | tail -n1)
          
          if [ -n "$previous_tag" ]; then
            # 如果有上一个tag，获取两个tag之间的提交
            changelog=$(git log ${previous_tag}..${current_tag} --pretty=format:"* %s" --no-merges)
          else
            # 如果是第一个tag，获取所有提交
            changelog=$(git log ${current_tag} --pretty=format:"* %s" --no-merges)
          fi
          
          # 如果changelog为空，添加一个默认消息
          if [ -z "$changelog" ]; then
            changelog="* 首次发布"
          fi
          
          # 将changelog保存到文件
          echo "$changelog" > CHANGELOG.md
          # 将changelog设置为输出变量，不进行转义处理
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$changelog" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: create release
        id: create-release
        uses: actions/github-script@v6
        with:
          script: |
            const version = process.env.PACKAGE_VERSION;
            const now = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
            const changelog = `${{ steps.changelog.outputs.changelog }}`;
            const { data } = await github.rest.repos.createRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag_name: `v${version}`,
              name: `EmbedTalk v${version}`,
              body: `# EmbedTalk v${version} 更新说明\n\n## 更新内容\n${changelog}\n\n## 发布时间\n${now}`,
              draft: true,
              prerelease: false
            })
            return data.id

  build-tauri:
    needs: create-release
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: "macos-latest"
            args: "--target x86_64-apple-darwin"
          - platform: "windows-latest"
            args: ""

    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
          registry-url: 'https://registry.npmmirror.com'

      - name: install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-22.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.0-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 7

      - name: install frontend dependencies
        run: pnpm install

      - uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_KEY_PASSWORD }}
        with:
          releaseId: ${{ needs.create-release.outputs.release_id }}
          releaseBody: ${{ needs.create-release.outputs.changelog }}
          args: ${{ matrix.args }}
          updaterJsonPreferNsis: true
          updaterJsonKeepUniversal: false

  publish-release:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    needs: [create-release, build-tauri]

    steps:
      - name: publish release
        id: publish-release
        uses: actions/github-script@v6
        env:
          release_id: ${{ needs.create-release.outputs.release_id }}
        with:
          script: |
            github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: process.env.release_id,
              draft: false,
              prerelease: false
            })
