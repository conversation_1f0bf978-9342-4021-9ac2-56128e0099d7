# 系统托盘功能

本文档介绍了EmbedTalk应用程序的系统托盘功能实现。

## 功能概述

系统托盘功能允许用户：
- 将应用程序最小化到系统托盘
- 配置关闭按钮的行为（最小化到托盘或完全退出）
- 通过托盘图标快速访问应用程序
- 自定义托盘行为设置

## 技术实现

### 前端组件

#### 1. 设置管理 (`src/store/slices/settingsSlice.ts`)
- 扩展了现有的设置slice，添加了托盘相关设置
- 支持的设置项：
  - `minimizeToTray`: 最小化到托盘
  - `closeToTray`: 关闭到托盘
  - `startMinimized`: 启动时最小化
  - `showTrayNotifications`: 显示托盘通知

#### 2. 关闭确认对话框 (`src/components/ui/CloseConfirmDialog.tsx`)
- 使用DaisyUI样式的模态对话框
- 提供"最小化到托盘"和"完全退出"选项
- 支持"记住选择"功能

#### 3. 托盘管理Hook (`src/hooks/useTrayManager.ts`)
- 封装托盘相关的逻辑
- 处理窗口显示/隐藏
- 管理关闭确认对话框状态

#### 4. 托盘提供者 (`src/context/TrayProvider.tsx`)
- 集成到现有的TrayProvider中
- 监听窗口关闭事件
- 渲染关闭确认对话框

#### 5. 设置界面 (`src/components/settings/TraySettings.tsx`)
- 用户友好的设置界面
- 实时预览设置效果
- 包含使用说明

### 后端实现

#### 1. 托盘处理器 (`src-tauri/src/taurihandler/tray_handler.rs`)
- 实现系统托盘的创建和管理
- 提供Tauri命令接口：
  - `init_system_tray`: 初始化系统托盘
  - `show_tray_notification`: 显示托盘通知
  - `exit_app`: 退出应用程序
  - `show_main_window`: 显示主窗口
  - `hide_main_window`: 隐藏主窗口
  - `is_window_visible`: 检查窗口可见性

#### 2. 主程序修改 (`src-tauri/src/main.rs`)
- 添加托盘命令处理器
- 修改窗口关闭事件处理
- 发送关闭请求事件到前端

#### 3. 配置更新 (`src-tauri/tauri.conf.json`)
- 启用托盘图标配置
- 设置托盘图标路径和属性

## 使用方法

### 1. 基本操作

**托盘图标操作：**
- 单击托盘图标：显示/隐藏应用程序窗口
- 双击托盘图标：显示应用程序窗口
- 右键托盘图标：显示托盘菜单

**窗口操作：**
- 点击关闭按钮：根据设置显示确认对话框或直接执行操作
- 最小化按钮：根据设置最小化到托盘或任务栏

### 2. 设置配置

访问 `/tray-test` 路由可以测试托盘功能和配置设置。

**可配置选项：**
- **最小化到托盘**: 点击最小化按钮时隐藏到托盘
- **关闭到托盘**: 点击关闭按钮时隐藏到托盘
- **启动时最小化**: 应用程序启动时直接最小化到托盘
- **显示托盘通知**: 最小化到托盘时显示系统通知

### 3. 开发测试

1. 启动应用程序
2. 访问 `/tray-test` 页面
3. 测试各种托盘操作
4. 调整设置并验证行为

## 平台兼容性

- **Windows**: 完全支持，托盘图标显示在系统托盘区域
- **macOS**: 支持，托盘图标显示在菜单栏
- **Linux**: 支持，但可能需要桌面环境支持系统托盘

## 注意事项

1. **权限要求**: 某些系统可能需要授权应用程序访问系统托盘
2. **图标资源**: 确保托盘图标文件存在且格式正确
3. **内存管理**: 托盘功能会保持应用程序在后台运行
4. **用户体验**: 提供清晰的视觉反馈和操作说明

## 故障排除

### 托盘图标不显示
1. 检查系统是否支持系统托盘
2. 确认图标文件路径正确
3. 查看控制台错误信息

### 窗口无法显示/隐藏
1. 检查窗口状态管理
2. 确认Tauri命令正常工作
3. 查看前端事件监听

### 设置不生效
1. 确认Redux状态更新
2. 检查localStorage存储
3. 验证组件重新渲染

## 扩展功能

未来可以考虑添加的功能：
- 托盘菜单自定义
- 快捷键支持
- 多窗口托盘管理
- 托盘气泡通知
- 托盘图标动画
