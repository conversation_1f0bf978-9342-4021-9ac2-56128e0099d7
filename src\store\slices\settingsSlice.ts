import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';

export interface SettingsState {
    region:  "南网" | "云南" | "广东" | "深圳" | "广西" | "贵州" | "海南" | "topo";
    theme: 'light' | 'dark' | 'system';
    // 托盘设置
    minimizeToTray: boolean;
    closeToTray: boolean;
    startMinimized: boolean;
    showTrayNotifications: boolean;
}

// 从localStorage获取初始状态，如果没有则使用默认值
const getInitialState = (): SettingsState => {
    const savedRegion = localStorage.getItem('region');
    const savedTheme = localStorage.getItem('theme');
    const savedMinimizeToTray = localStorage.getItem('minimizeToTray');
    const savedCloseToTray = localStorage.getItem('closeToTray');
    const savedStartMinimized = localStorage.getItem('startMinimized');
    const savedShowTrayNotifications = localStorage.getItem('showTrayNotifications');

    return {
        region: (savedRegion as SettingsState['region']) || '南网',
        theme: (savedTheme as SettingsState['theme']) || 'system',
        minimizeToTray: savedMinimizeToTray ? JSON.parse(savedMinimizeToTray) : true,
        closeToTray: savedCloseToTray ? JSON.parse(savedCloseToTray) : true,
        startMinimized: savedStartMinimized ? JSON.parse(savedStartMinimized) : false,
        showTrayNotifications: savedShowTrayNotifications ? JSON.parse(savedShowTrayNotifications) : true
    };
};

export const settingsSlice = createSlice({
    name: 'settings',
    initialState: getInitialState(),
    reducers: {
        setRegion: (state, action: PayloadAction<SettingsState['region']>) => {
            state.region = action.payload;
            localStorage.setItem('region', action.payload);
        },
        setTheme: (state, action: PayloadAction<SettingsState['theme']>) => {
            state.theme = action.payload;
            localStorage.setItem('theme', action.payload);
        },
        setMinimizeToTray: (state, action: PayloadAction<boolean>) => {
            state.minimizeToTray = action.payload;
            localStorage.setItem('minimizeToTray', JSON.stringify(action.payload));
        },
        setCloseToTray: (state, action: PayloadAction<boolean>) => {
            state.closeToTray = action.payload;
            localStorage.setItem('closeToTray', JSON.stringify(action.payload));
        },
        setStartMinimized: (state, action: PayloadAction<boolean>) => {
            state.startMinimized = action.payload;
            localStorage.setItem('startMinimized', JSON.stringify(action.payload));
        },
        setShowTrayNotifications: (state, action: PayloadAction<boolean>) => {
            state.showTrayNotifications = action.payload;
            localStorage.setItem('showTrayNotifications', JSON.stringify(action.payload));
        }
    }
});

// 导出 actions
export const {
    setRegion,
    setTheme,
    setMinimizeToTray,
    setCloseToTray,
    setStartMinimized,
    setShowTrayNotifications
} = settingsSlice.actions;

// 导出 selectors
export const selectRegion = (state: RootState) => state.settings.region;
export const selectTheme = (state: RootState) => state.settings.theme;
export const selectMinimizeToTray = (state: RootState) => state.settings.minimizeToTray;
export const selectCloseToTray = (state: RootState) => state.settings.closeToTray;
export const selectStartMinimized = (state: RootState) => state.settings.startMinimized;
export const selectShowTrayNotifications = (state: RootState) => state.settings.showTrayNotifications;

export default settingsSlice.reducer; 