cargo:rustc-cfg=desktop
cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=emabletalk
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\target\debug\build\EmbedTalk-67a5e2fea740cf5e\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
cargo:rerun-if-changed=resources\protocolconfig\CSG13.xml
cargo:rerun-if-changed=resources\protocolconfig\CSG16.xml
cargo:rerun-if-changed=resources\protocolconfig\DLT645.xml
cargo:rerun-if-changed=resources\protocolconfig\MOUDLE.xml
cargo:rerun-if-changed=resources\taskoadconfig\50020200_list.yml
cargo:rerun-if-changed=resources\taskoadconfig\50040200_list.yml
cargo:rerun-if-changed=resources\taskoadconfig\50060200_list.yml
cargo:rerun-if-changed=resources\taskoadconfig\oad_list.yml
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\ProjackSpace\projectspace\EmbedTalk\src-tauri\target\debug\build\EmbedTalk-67a5e2fea740cf5e\out\resource.lib
