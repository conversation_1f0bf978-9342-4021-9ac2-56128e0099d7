[package]
name = "EmbedTalk"
version = "0.0.11"
description = "EmbedTalk"
authors = ["zerojack"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.2.0", features = ["config-json5"] }

[profile.dev]
debug = true

[profile.release]
debug = true

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
quick-xml = { version = "0.37.5", features = ["serialize"] }
lazy_static = "1.5"
regex = "1"
hex = "0.4"
num-derive = "0.3"
num-traits = "0.2"
erased-serde = "0.3"
toml="0.5"
tokio = { version = "1", features = ["full"] }
tracing = "0.1"
tracing-subscriber = "0.3"
tracing-appender = "0.2"
time = "=0.3.36"  # 明确指定版本
backtrace = "0.3"
rayon = "1.10"
btleplug = "0.10"
bincode = "1.3"
async-trait = "0.1"
uuid = { version = "1", features = ["v4"] }
rumqttc = "0.15"
tokio-serial = "5.4"
socket2 = "0.4"
once_cell = "1.21"
dirs-next = "2.0"
serde_yaml = "0.9"
windows = { version = "0.48", features = ["Win32_Foundation", "Win32_UI_WindowsAndMessaging", "UI_ViewManagement"] }
objc = "0.2.7"
chrono = { version = "0.4", features = ["serde"] }

# Web服务器依赖
axum = { version = "0.7", optional = true }
tower-http = { version = "0.5", features = ["cors"], optional = true }
tower = { version = "0.4", optional = true }

# 桌面应用依赖
tauri = { version = "2", features = [ "macos-private-api", "tray-icon", "config-json5", "devtools"], optional = true }
tauri-plugin-os = { version = "2.2.1", optional = true }
tauri-plugin-shell = { version = "2.2.1", optional = true }
tauri-plugin-global-shortcut = { version = "2.2.0", optional = true }
tauri-plugin-fs = { version = "2.2.1", optional = true }
tauri-plugin-dialog = { version = "2.2.1", optional = true }
tauri-plugin-clipboard-manager = { version = "2.2.2", optional = true }
tauri-plugin-process = { version = "2.2.1", optional = true }
tauri-plugin-log = { version = "2.4.0", optional = true }
tauri-plugin-sql = { version = "2.2.0", features = ["sqlite"], optional = true }
tauri-plugin-updater = { version = "2", optional = true }
tauri-plugin-window-state = { version = "2.2.2", optional = true }

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = { version = "0.26.0", optional = true }

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-global-shortcut = "2.2.0"
tauri-plugin-updater = "2"
tauri-plugin-window-state = "2.2.2"

[target.'cfg(not(target_os = "windows"))'.dependencies]
windows = { version = "0.48", features = [] }

[features]
# 默认feature
default = ["desktop"]

# 桌面应用feature
desktop = [
    "tauri",
    "tauri-plugin-os",
    "tauri-plugin-shell",
    "tauri-plugin-global-shortcut",
    "tauri-plugin-fs",
    "tauri-plugin-dialog",
    "tauri-plugin-clipboard-manager",
    "tauri-plugin-process",
    "tauri-plugin-log",
    "tauri-plugin-sql",
    "tauri-plugin-updater",
    "tauri-plugin-window-state",
    "cocoa"
]

# Web服务器feature
web = [
    "axum",
    "tower-http",
    "tower"
]

# 这个feature用于生产构建
custom-protocol = ["tauri/custom-protocol"]
